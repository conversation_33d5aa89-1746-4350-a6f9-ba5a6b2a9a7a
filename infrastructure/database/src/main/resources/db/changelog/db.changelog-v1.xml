<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="add-start-end-to-manufacturingtask" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_task
                ADD COLUMN start_time TIMESTAMP,
                ADD COLUMN end_time   TIMESTAMP;

            UPDATE manufacturing_task SET start_time=estimated_start_time where start_time is null;
            UPDATE manufacturing_task SET end_time=estimated_end_time where end_time is null;
        </sql>
    </changeSet>

    <changeSet id="add-servicetemplate" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS service_template
            (
                id                  UUID PRIMARY KEY,
                create_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted             BOOLEAN   NOT NULL DEFAULT FALSE,
                owner_id            UUID REFERENCES account (id),
                name                VARCHAR(200),
                sell_price_amount   BIGINT,
                sell_price_currency VARCHAR(3)
            );

            DROP TRIGGER IF EXISTS service_template_timestamp on service_template;
            CREATE TRIGGER service_template_timestamp
                BEFORE INSERT OR UPDATE
                ON service_template
                FOR EACH ROW
            EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-services-to-salesorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE sales_order
                RENAME COLUMN products TO items;
        </sql>
    </changeSet>

    <changeSet id="add-proforma-invoice" author="csaba.birtalan">
        <sql>
            ALTER TABLE invoice ADD COLUMN proforma BOOLEAN NOT NULL DEFAULT FALSE;
        </sql>
    </changeSet>

    <changeSet id="add-mo-so-constraint" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order ADD UNIQUE (sales_order_id, product_id);
        </sql>
    </changeSet>

    <changeSet id="add-users-table" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS users
            (
                id          UUID PRIMARY KEY,
                create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted     BOOLEAN      NOT NULL DEFAULT FALSE,
                owner_id    UUID         NOT NULL UNIQUE REFERENCES account (id),
                external_id VARCHAR(200) NOT NULL UNIQUE,
                name        VARCHAR(200)
            );

            DROP TRIGGER IF EXISTS users_timestamp ON users;
            CREATE TRIGGER users_timestamp
                BEFORE INSERT OR UPDATE
                ON users
                FOR EACH ROW
            EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-notification-table" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS notification
            (
                id               UUID PRIMARY KEY,
                create_time      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted          BOOLEAN     NOT NULL DEFAULT FALSE,
                owner_id         UUID        NOT NULL UNIQUE REFERENCES account (id),
                type             VARCHAR(20) NOT NULL,
                section          VARCHAR(20) NOT NULL,
                target_entity_id UUID,
                resolved_at      TIMESTAMP,
                resolved_by      UUID REFERENCES users (id)
            );

            DROP TRIGGER IF EXISTS notification_timestamp ON notification;
            CREATE TRIGGER notification_timestamp
                BEFORE INSERT OR UPDATE
                ON notification
                FOR EACH ROW
            EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-firebase-registration-token-table" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS firebase_registration_token
            (
                id          UUID PRIMARY KEY,
                create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted     BOOLEAN      NOT NULL DEFAULT FALSE,
                owner_id    UUID         NOT NULL REFERENCES account (id),
                token       VARCHAR(200) NOT NULL,
                user_id     UUID UNIQUE REFERENCES users (id),
                employee_id UUID UNIQUE REFERENCES employee (id)
            );

            DROP TRIGGER IF EXISTS firebase_registration_token_timestamp ON firebase_registration_token;
            CREATE TRIGGER firebase_registration_token_timestamp
                BEFORE INSERT OR UPDATE
                ON firebase_registration_token
                FOR EACH ROW
            EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="invoice-drop-unique-salesorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE invoice DROP CONSTRAINT invoice_sales_order_id_key;
            ALTER TABLE invoice ADD UNIQUE (sales_order_id, proforma);
        </sql>
    </changeSet>

    <changeSet id="move-expected-by-to-purchase-order" author="csaba.birtalan">
        <sql>
            ALTER TABLE purchase_wishlist DROP COLUMN expected_delivery;
            ALTER TABLE purchase_order ADD COLUMN expected_by TIMESTAMP;
        </sql>
    </changeSet>

    <changeSet id="notification-read-by-table" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS notification_read_by
            (
                notification_id UUID NOT NULL REFERENCES notification (id),
                user_id         UUID NOT NULL REFERENCES users (id)
            );
        </sql>
    </changeSet>

    <changeSet id="add-ranking-to-manufacturing-task" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_task ADD COLUMN ranking BIGINT;
        </sql>
    </changeSet>

    <changeSet id="remove-owner-constraint-from-notification" author="csaba.birtalan">
        <sql>
            ALTER TABLE notification DROP CONSTRAINT notification_owner_id_key;
        </sql>
    </changeSet>

    <changeSet id="add-triggered-by-to-notification" author="csaba.birtalan">
        <sql>
            ALTER TABLE notification ADD COLUMN triggered_by VARCHAR(200);
        </sql>
    </changeSet>

    <changeSet id="add-unique-to-notification-read-by" author="csaba.birtalan">
        <sql>
            ALTER TABLE notification_read_by ADD UNIQUE (notification_id, user_id);
        </sql>
    </changeSet>

    <changeSet id="add-details-to-notification" author="csaba.birtalan">
        <sql>
            ALTER TABLE notification ADD COLUMN details JSONB;
        </sql>
    </changeSet>

    <changeSet id="remove-owner-constraint-from-users" author="csaba.birtalan">
        <sql>
            ALTER TABLE users DROP CONSTRAINT users_owner_id_key;
        </sql>
    </changeSet>

    <changeSet id="remove-servicetemplate" author="csaba.birtalan">
        <sql>
            DROP TABLE IF EXISTS service_template;
            DROP TRIGGER IF EXISTS service_template_timestamp on service_template;
        </sql>
    </changeSet>

    <changeSet id="add-custom-to-manufacturingorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order ADD COLUMN custom_product BOOLEAN DEFAULT FALSE;
        </sql>
    </changeSet>

    <changeSet id="remove-notnull-constraint-from-materialgood-code" author="csaba.birtalan">
        <sql>
            ALTER TABLE material_good ALTER code DROP NOT NULL;
        </sql>
    </changeSet>

    <changeSet id="add-employee-timeoff" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS employee_timeoff
            (
                id                  UUID PRIMARY KEY,
                create_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted             BOOLEAN   NOT NULL DEFAULT FALSE,
                owner_id            UUID REFERENCES account (id),
                employee_id         UUID REFERENCES employee (id),
                start_time          TIMESTAMP NOT NULL,
                end_time            TIMESTAMP NOT NULL
                );

            DROP TRIGGER IF EXISTS employee_timeoff_timestamp on employee_timeoff;
            CREATE TRIGGER employee_timeoff_timestamp
                BEFORE INSERT OR UPDATE
                                     ON employee_timeoff
                                     FOR EACH ROW
                                     EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-inventory-type" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS inventory_type
            (
                id                  UUID PRIMARY KEY,
                create_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted             BOOLEAN   NOT NULL DEFAULT FALSE,
                owner_id            UUID REFERENCES account (id),
                name                VARCHAR(200)
                );

            DROP TRIGGER IF EXISTS inventory_type_timestamp on inventory_type;
            CREATE TRIGGER inventory_type_timestamp
                BEFORE INSERT OR UPDATE
                                     ON inventory_type
                                     FOR EACH ROW
                                     EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-type-to-inventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE inventory ADD COLUMN type_id UUID REFERENCES inventory_type (id);
        </sql>
    </changeSet>

    <changeSet id="add-type-to-inventoryadjustmentorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE inventory_adjustment_order ADD COLUMN type VARCHAR(20);
            UPDATE inventory_adjustment_order SET type='ADJUSTMENT'
        </sql>
    </changeSet>

    <changeSet id="add-receptionreceipt" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS reception_receipt
            (
                id                  UUID PRIMARY KEY,
                create_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted             BOOLEAN   NOT NULL DEFAULT FALSE,
                owner_id            UUID REFERENCES account (id),
                supplier_id         UUID REFERENCES company (id),
                purchase_order_id   UUID REFERENCES purchase_order (id),
                number              VARCHAR(20) NOT NULL,
                reception_date      TIMESTAMP NOT NULL,
                supporting_document JSONB NOT NULL,
                details             JSONB NOT NULL,
                goods               JSONB NOT NULL
            );

            DROP TRIGGER IF EXISTS reception_receipt_timestamp on reception_receipt;
            CREATE TRIGGER reception_receipt_timestamp
                BEFORE INSERT OR UPDATE
                                     ON reception_receipt
                                     FOR EACH ROW
                                     EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-materialissuenote" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS material_issue_note
            (
                id                     UUID PRIMARY KEY,
                create_time            TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time            TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted                BOOLEAN     NOT NULL DEFAULT FALSE,
                owner_id               UUID REFERENCES account (id),
                manufacturing_order_id UUID REFERENCES manufacturing_order (id),
                number                 VARCHAR(20) NOT NULL,
                date                   TIMESTAMP   NOT NULL,
                details                JSONB       NOT NULL,
                materials              JSONB       NOT NULL
            );

            DROP TRIGGER IF EXISTS material_issue_note_timestamp on material_issue_note;
            CREATE TRIGGER material_issue_note_timestamp
                BEFORE INSERT OR UPDATE
                                     ON material_issue_note
                                     FOR EACH ROW
                                     EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-type-to-reserved-inventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE reserved_inventory ADD COLUMN inventory_type_id UUID REFERENCES inventory_type (id);
        </sql>
    </changeSet>

    <changeSet id="add-reception-receipt-to-inventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE inventory ADD COLUMN reception_receipt_id UUID REFERENCES reception_receipt (id);
        </sql>
    </changeSet>

    <changeSet id="remove-mo-so-constraint" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order DROP CONSTRAINT manufacturing_order_sales_order_id_product_id_key;
        </sql>
    </changeSet>

    <changeSet id="add-mandatory-for-order-to-reserved-inventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE reserved_inventory ADD COLUMN mandatory_for_order BOOLEAN NOT NULL DEFAULT FALSE;
        </sql>
    </changeSet>
    <changeSet id="add-from-order-to-reserved-inventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE reserved_inventory ADD COLUMN from_order UUID;
        </sql>
    </changeSet>

    <changeSet id="remove-sales-order-non-null--constraint" author="csaba.birtalan">
        <sql>
            ALTER TABLE sales_order ALTER COLUMN delivery_deadline DROP NOT NULL ;
        </sql>
    </changeSet>

    <changeSet id="add-preferential-to-employee-manufacturingoperationtemplate" author="csaba.birtalan">
        <sql>
            ALTER TABLE employee_manufacturingoperationtemplate ADD COLUMN preferential BOOLEAN NOT NULL DEFAULT FALSE;
        </sql>
    </changeSet>

    <changeSet id="add-text-search" author="csaba.birtalan">
        <sql>
            ALTER TABLE material_good
                ADD COLUMN text_search tsvector
                    GENERATED ALWAYS AS (to_tsvector('simple', coalesce(name, '') || ' ' || coalesce(code, ''))) STORED;
            CREATE INDEX ON material_good USING GIN (text_search);

            ALTER TABLE sales_order
                ADD COLUMN text_search tsvector
                    GENERATED ALWAYS AS (to_tsvector('simple', coalesce(number, ''))) STORED;
            CREATE INDEX ON sales_order USING GIN (text_search);

            ALTER TABLE purchase_order
                ADD COLUMN text_search tsvector
                    GENERATED ALWAYS AS (to_tsvector('simple', coalesce(number, ''))) STORED;
            CREATE INDEX ON purchase_order USING GIN (text_search);

            ALTER TABLE manufacturing_order
                ADD COLUMN text_search tsvector
                    GENERATED ALWAYS AS (to_tsvector('simple', coalesce(number, ''))) STORED;
            CREATE INDEX ON manufacturing_order USING GIN (text_search);

            ALTER TABLE company ADD COLUMN text_search tsvector;
            CREATE INDEX ON company USING GIN (text_search);

            ALTER TABLE employee
                ADD COLUMN text_search tsvector
                    GENERATED ALWAYS AS (to_tsvector('simple', coalesce(name, '') || ' ' || coalesce(details ->> 'position', ''))) STORED;
            CREATE INDEX ON employee USING GIN (text_search);
        </sql>
    </changeSet>

    <changeSet id="add-jobstatus" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS job_status
            (
                create_time TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                name        VARCHAR(50) NOT NULL,
                status      BOOLEAN     NOT NULL DEFAULT FALSE
            );
        </sql>
    </changeSet>

    <changeSet id="remove-default-supplier-from-material-good" author="csaba.birtalan">
        <sql>
            ALTER TABLE material_good DROP COLUMN default_supplier;
        </sql>
    </changeSet>

    <changeSet id="remove-deleted-wishlist-items" author="csaba.birtalan">
        <sql>
            DELETE FROM purchase_wishlist WHERE deleted = true;
        </sql>
    </changeSet>

    <changeSet id="add-constraint-to-wishlist" author="csaba.birtalan">
        <sql>
            ALTER TABLE purchase_wishlist ADD UNIQUE (owner_id, material_good_id, supplier_id);
        </sql>
    </changeSet>

    <changeSet id="add-file-product" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS file
            (
                create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
                id          UUID PRIMARY KEY,
                owner_id    UUID         NOT NULL REFERENCES account (id),
                file_name   VARCHAR(200) NOT NULL,
                file_size   INTEGER      NOT NULL
            );

            CREATE TABLE IF NOT EXISTS product_file
            (
                create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                owner_id    UUID      NOT NULL REFERENCES account (id),
                product_id  UUID      NOT NULL REFERENCES material_good (id),
                file_id     UUID      NOT NULL REFERENCES file (id)
            );
        </sql>
    </changeSet>

    <changeSet id="change-materials-for-products" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET product_details = jsonb_set(
                    product_details,
                    '{materials}',
                    (SELECT jsonb_agg(
                                    jsonb_build_object(
                                            'materialIds', jsonb_build_array(old_element -> 'a'),
                                            'quantity', old_element -> 'b',
                                            'optional', false,
                                            'configurableWithOptions', false,
                                            'replaceableWithOptions', false
                                    )
                            )
                     FROM jsonb_array_elements(product_details -> 'materials') AS old_element)::jsonb
                                  );
        </sql>
    </changeSet>

    <changeSet id="change-materials-for-mo" author="csaba.birtalan">
        <sql>
            UPDATE manufacturing_order
            SET required_materials =
                    (SELECT jsonb_agg(
                                    jsonb_build_object(
                                            'materialIds', jsonb_build_array(old_element -> 'a'),
                                            'quantity', old_element -> 'b'
                                    )
                            )
                     FROM jsonb_array_elements(required_materials) AS old_element)::jsonb;
        </sql>
    </changeSet>

    <changeSet id="add_subassembly_to_product" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET product_details = jsonb_set(product_details, '{isSubassembly}', 'false'::jsonb)
            WHERE material_good_type = 'PRODUCT';
        </sql>
    </changeSet>

    <changeSet id="change-type-designations-for-account" author="csaba.birtalan">
        <sql>
            UPDATE account
            SET settings = jsonb_set(
                    settings,
                    '{general, inventoryAccountingSettings, typeDesignations}',
                    jsonb_build_object(
                            'typesForSales', jsonb_build_array(settings -> 'general' -> 'inventoryAccountingSettings' -> 'typeDesignations' -> 'typeForSales'),
                            'typesForManufacturing', jsonb_build_array(settings -> 'general' -> 'inventoryAccountingSettings' -> 'typeDesignations' -> 'typeForManufacturing')
                    )
            );
        </sql>
    </changeSet>

    <changeSet id="change-price-in-inventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE inventory RENAME COLUMN price_amount TO reception_price_amount;
            ALTER TABLE inventory RENAME COLUMN price_currency TO reception_price_currency;
        </sql>
    </changeSet>
    <changeSet id="add-exit-in-inventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE inventory ADD COLUMN exit_price_amount BIGINT;
            ALTER TABLE inventory ADD COLUMN exit_price_currency VARCHAR(3);
        </sql>
    </changeSet>

    <changeSet id="add-servicetemplate-back" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS service_template
            (
                id                  UUID PRIMARY KEY,
                create_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted             BOOLEAN   NOT NULL DEFAULT FALSE,
                owner_id            UUID REFERENCES account (id),
                name                VARCHAR(200)
                );

            DROP TRIGGER IF EXISTS service_template_timestamp on service_template;
            CREATE TRIGGER service_template_timestamp
                BEFORE INSERT OR UPDATE
                                     ON service_template
                                     FOR EACH ROW
                                     EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-service-id-to-mo" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order ADD COLUMN service_id UUID references service_template(id);
        </sql>
    </changeSet>

    <changeSet id="add-executed-services" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS executed_services
            (
                create_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted                BOOLEAN   NOT NULL DEFAULT FALSE,
                owner_id               UUID REFERENCES account (id),
                sales_order_id         UUID references sales_order (id),
                manufacturing_order_id UUID references manufacturing_order (id),
                service_id             UUID references service_template (id),
                quantity               NUMERIC NOT NULL,
                cost_amount            BIGINT,
                sale_amount            BIGINT,
                service_currency       VARCHAR(3) NOT NULL,
                PRIMARY KEY (owner_id, sales_order_id, manufacturing_order_id, service_id)
                );

            DROP TRIGGER IF EXISTS executed_services_timestamp on executed_services;
            CREATE TRIGGER executed_services_timestamp
                BEFORE INSERT OR UPDATE
                                     ON executed_services
                                     FOR EACH ROW
                                     EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="delete-so-with-services" author="csaba.birtalan">
        <sql>
            update sales_order set deleted = true where id in (select id from sales_order, jsonb_array_elements(items) AS item WHERE item->>'name' IS NOT NULL);
        </sql>
    </changeSet>

    <changeSet id="delete-name-from-so-items" author="csaba.birtalan">
        <sql>
            WITH updated_items AS (SELECT id, jsonb_agg(obj - 'name') AS new_item
                                   FROM sales_order,
                                        jsonb_array_elements(items) AS obj
                                   GROUP BY id)
            UPDATE sales_order
            SET items = updated_items.new_item
                FROM updated_items
            WHERE sales_order.id = updated_items.id;
        </sql>
    </changeSet>

    <changeSet id="delete-duration-from-operation-templates" author="csaba.birtalan">
        <sql>
            UPDATE manufacturing_operation_template SET details = details - 'durationInMinutes';
        </sql>
    </changeSet>

    <changeSet id="cleanup-before-adding-constraint-for-only-one-mo-per-so-and-item" author="csaba.birtalan">
        <sql>
            delete from reserved_inventory where manufacturing_order_id in (select id from manufacturing_order where manufacturing_order.deleted=true);
            delete from manufacturing_task where manufacturing_order_id in (select id from manufacturing_order where manufacturing_order.deleted=true);
            delete from manufacturing_order where manufacturing_order.deleted=true;
        </sql>
    </changeSet>

    <changeSet id="add-constraint-for-only-one-mo-per-so-and-item" author="csaba.birtalan">
        <sql>
            -- Drop the existing unique constraint if it exists
            DROP INDEX IF EXISTS unique_so_mo_item_product;
            DROP INDEX IF EXISTS unique_so_mo_item_service;

            -- Create a unique index for the case where product_id is not NULL and service_id is NULL
            CREATE UNIQUE INDEX unique_so_mo_item_product
                ON manufacturing_order(sales_order_id, product_id)
                WHERE product_id IS NOT NULL AND service_id IS NULL;

            -- Create a unique index for the case where service_id is not NULL and product_id is NULL
            CREATE UNIQUE INDEX unique_so_mo_item_service
                ON manufacturing_order(sales_order_id, service_id)
                WHERE service_id IS NOT NULL AND product_id IS NULL;
        </sql>
    </changeSet>

    <changeSet id="revert-change-type-designations-for-account" author="csaba.birtalan">
        <sql>
            UPDATE account
            SET settings = jsonb_set(
                    settings,
                    '{general, inventoryAccountingSettings, typeDesignations}',
                    jsonb_build_object(
                            'typeForSales',
                            settings #> '{general, inventoryAccountingSettings, typeDesignations, typesForSales, 0}',
                            'typeForManufacturing',
                            settings #> '{general, inventoryAccountingSettings, typeDesignations, typesForManufacturing, 0}'
                    )
                           );
        </sql>
    </changeSet>

    <changeSet id="change-type-to-subassembly-for-material-good" author="csaba.birtalan">
        <sql>
            update material_good
            set material_good_type='SUBASSEMBLY'
            where (product_details -> 'isSubassembly')::boolean = true;
        </sql>
    </changeSet>

    <changeSet id="delete-isSubassembly-from-material-good" author="csaba.birtalan">
        <sql>
            update material_good
            set product_details=product_details - 'isSubassembly'
            where product_details is not null;
        </sql>
    </changeSet>

    <changeSet id="add-customer-note-table" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS customer_note
            (
                id               UUID PRIMARY KEY,
                create_time      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted          BOOLEAN     NOT NULL DEFAULT FALSE,
                owner_id         UUID        NOT NULL REFERENCES account (id),
                company_id       UUID        NOT NULL REFERENCES company (id),
                added_by_id      UUID        NOT NULL REFERENCES users (id),
                note             TEXT        NOT NULL
                );

            DROP TRIGGER IF EXISTS customer_note_timestamp ON customer_note;
            CREATE TRIGGER customer_note_timestamp
                BEFORE INSERT OR UPDATE
                                     ON customer_note
                                     FOR EACH ROW
                                     EXECUTE PROCEDURE update_timestamp();
        </sql>
    </changeSet>

    <changeSet id="add-type-to-company" author="csaba.birtalan">
        <sql>
            ALTER TABLE company ADD COLUMN company_type VARCHAR(25) NOT NULL DEFAULT 'LOCAL_LEGAL_ENTITY';
        </sql>
    </changeSet>

    <changeSet id="change-inventory-adjustment-from-type" author="csaba.birtalan">
        <sql>
            UPDATE inventory_adjustment_order
            SET details = (
                WITH updated_entries AS (
                    SELECT jsonb_agg(
                                   jsonb_set(entry, '{fromType}', details->'fromType')
                           ) AS new_entries
                    FROM jsonb_array_elements(details->'inventoryEntries') AS entry
                )
                SELECT jsonb_set(
                               details - 'fromType',
                               '{inventoryEntries}',
                               updated_entries.new_entries
                       )
                FROM updated_entries
            )
            WHERE type='TYPE_CHANGE' and details->'fromType' IS NOT NULL;

            UPDATE inventory_adjustment_order SET details = details - 'fromType';
        </sql>
    </changeSet>

    <changeSet id="add-file-to-mo-so" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS manufacturingorder_file
            (
                create_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                owner_id               UUID      NOT NULL REFERENCES account (id),
                manufacturing_order_id UUID      NOT NULL REFERENCES manufacturing_order (id),
                file_id                UUID      NOT NULL REFERENCES file (id)
                );

            CREATE TABLE IF NOT EXISTS salesorder_file
            (
                create_time    TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                owner_id       UUID      NOT NULL REFERENCES account (id),
                sales_order_id UUID      NOT NULL REFERENCES sales_order (id),
                file_id        UUID      NOT NULL REFERENCES file (id)
                );
        </sql>
    </changeSet>

    <changeSet id="add-systemevent" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS system_event
            (
                id               UUID PRIMARY KEY,
                create_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
                owner_id         UUID         NOT NULL REFERENCES account (id),
                user_id          UUID REFERENCES users (id),
                target_entity_id UUID         NOT NULL,
                section          VARCHAR(200) NOT NULL,
                transition       VARCHAR(200) NOT NULL
                );
        </sql>
    </changeSet>

    <changeSet id="add-goods-accompanying-note" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS goods_accompanying_note
            (
                id                            UUID PRIMARY KEY,
                create_time                   TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time                   TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted                       BOOLEAN      NOT NULL DEFAULT FALSE,
                owner_id                      UUID         NOT NULL REFERENCES account (id),
                customer_id                   UUID REFERENCES company (id),
                sales_order_id                UUID REFERENCES sales_order (id),
                number                        VARCHAR(200) NOT NULL,
                delivery_date                 TIMESTAMP    NOT NULL,
                from_address                  JSONB        NOT NULL,
                to_address                    JSONB        NOT NULL,
                delegate                      JSONB        NOT NULL,
                transport_registration_number VARCHAR(20),
                notes                         VARCHAR(500),
                items                         JSONB
            );
        </sql>
    </changeSet>

    <changeSet id="add-notes-to-invoice" author="csaba.birtalan">
        <sql>
            ALTER TABLE invoice ADD COLUMN notes VARCHAR(5000);
        </sql>
    </changeSet>

    <changeSet id="set-19%-vat-for-products" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET product_details = jsonb_set(product_details, '{vatRate}', '0.19'::jsonb)
            WHERE material_good_type = 'PRODUCT';
        </sql>
    </changeSet>

    <changeSet id="set-19%-vat-for-invoice-items" author="csaba.birtalan">
        <sql>
            UPDATE invoice
            SET items = (
                SELECT jsonb_agg(jsonb_set(e, '{vatRate}', '0.19', true))
                FROM jsonb_array_elements(items) AS e
            );
        </sql>
    </changeSet>

    <changeSet id="add-logo-to-account" author="csaba.birtalan">
        <sql>
            ALTER TABLE account ADD COLUMN logo VARCHAR(5000);
        </sql>
    </changeSet>

    <changeSet id="remove-salesorder-invoice" author="csaba.birtalan">
        <sql>
            DROP TABLE IF EXISTS salesorder_invoice;
        </sql>
    </changeSet>

    <changeSet id="add-fields-to-servicetemplate" author="csaba.birtalan">
        <sql>
            ALTER TABLE service_template ADD COLUMN vat_rate NUMERIC;
            ALTER TABLE service_template ADD COLUMN measurement_unit VARCHAR(20);
            ALTER TABLE service_template ADD COLUMN sell_price_amount INTEGER;
            ALTER TABLE service_template ADD COLUMN sell_price_currency VARCHAR(3);
        </sql>
    </changeSet>

    <changeSet id="add-manufacturing-costs-to-manufacturing-order" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order ADD COLUMN manufacturing_costs JSONB;
        </sql>
    </changeSet>

    <changeSet id="rename-inventory-type-to-unit" author="csaba.birtalan">
        <sql>
            DROP TRIGGER IF EXISTS inventory_type_timestamp on inventory_type;
            ALTER TABLE inventory_type RENAME TO inventory_unit;
            DROP TRIGGER IF EXISTS inventory_unit_timestamp on inventory_unit;
            CREATE TRIGGER inventory_unit_timestamp
                BEFORE INSERT OR UPDATE
                                     ON inventory_unit
                                     FOR EACH ROW
                                     EXECUTE PROCEDURE update_timestamp();

            ALTER TABLE inventory RENAME COLUMN type_id TO unit_id;
            ALTER TABLE reserved_inventory RENAME COLUMN inventory_type_id TO inventory_unit_id;

            UPDATE account SET settings = settings #- '{general,inventoryAccountingSettings,typeDesignations}'
        </sql>
    </changeSet>

    <changeSet id="add-unit-of-production" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET product_details = jsonb_set(product_details, '{unitOfProduction}', '1')
            WHERE material_good_type IN ('PRODUCT', 'SUBASSEMBLY');
        </sql>
    </changeSet>

    <changeSet id="add-waste-percentage" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET product_details = jsonb_set(
                    product_details,
                    '{materials}',
                    COALESCE(
                            (SELECT jsonb_agg(jsonb_set(material, '{wastePercentage}', '0'::jsonb))
                             FROM jsonb_array_elements(product_details -> 'materials') AS material),
                            '[]'::jsonb -- Use an empty array if materials is empty
                    )
                                  )
            WHERE product_details -> 'materials' IS NOT NULL
              AND material_good_type IN ('PRODUCT', 'SUBASSEMBLY')
        </sql>
    </changeSet>

    <changeSet id="migrate-to-inventory-unit-reception-receipt" author="csaba.birtalan">
        <sql>
            WITH updated_data AS (
                SELECT
                    id,
                    jsonb_agg(
                            jsonb_set(
                                    elem #- '{inventoryTypeId}', -- Remove the old field
                                    '{inventoryUnitId}',         -- Add the new field
                                    elem->'inventoryTypeId'      -- Use the value from the old field
                            )
                    ) AS updated_json
                FROM (
                         SELECT
                             id,
                             jsonb_array_elements(goods) AS elem
                         FROM reception_receipt
                     ) sub
                GROUP BY id
            )
            UPDATE reception_receipt
            SET goods = updated_data.updated_json
                FROM updated_data
            WHERE reception_receipt.id = updated_data.id;
        </sql>
    </changeSet>

    <changeSet id="generic-notes-functionality" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS note
            (
                id               UUID PRIMARY KEY,
                create_time      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time      TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted          BOOLEAN     NOT NULL DEFAULT FALSE,
                owner_id         UUID        NOT NULL REFERENCES account (id),
                added_by_id      UUID        NOT NULL REFERENCES users (id),
                note             TEXT        NOT NULL
            );

            DROP TRIGGER IF EXISTS note_timestamp ON note;
            CREATE TRIGGER note_timestamp
                BEFORE INSERT OR UPDATE
                ON note
                FOR EACH ROW
            EXECUTE PROCEDURE update_timestamp();

            DROP TRIGGER IF EXISTS customer_note_timestamp ON customer_note;

            INSERT INTO note (id, create_time, update_time, deleted, owner_id, added_by_id, note)
            SELECT id, create_time, update_time, deleted, owner_id, added_by_id, note
            FROM customer_note;

            ALTER TABLE customer_note ADD COLUMN note_id UUID REFERENCES note (id);

            UPDATE customer_note SET note_id = id;

            ALTER TABLE customer_note
                DROP COLUMN id,
                DROP COLUMN create_time,
                DROP COLUMN update_time,
                DROP COLUMN deleted,
                DROP COLUMN owner_id,
                DROP COLUMN added_by_id,
                DROP COLUMN note;
        </sql>
    </changeSet>

    <changeSet id="notes-for-so-mo-po" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS sales_order_note
            (
                sales_order_id UUID NOT NULL REFERENCES sales_order (id),
                note_id        UUID NOT NULL REFERENCES note (id)
            );

            CREATE TABLE IF NOT EXISTS manufacturing_order_note
            (
                manufacturing_order_id UUID NOT NULL REFERENCES manufacturing_order (id),
                note_id                UUID NOT NULL REFERENCES note (id)
            );

            CREATE TABLE IF NOT EXISTS purchase_order_note
            (
                purchase_order_id UUID NOT NULL REFERENCES purchase_order (id),
                note_id           UUID NOT NULL REFERENCES note (id)
            );
        </sql>
    </changeSet>

    <changeSet id="correct-service-template-measurementunit" author="csaba.birtalan">
        <sql>
            UPDATE service_template SET measurement_unit = 'PIECE' WHERE measurement_unit is null;
        </sql>
    </changeSet>

    <changeSet id="add-externalcode-to-materialgood" author="csaba.birtalan">
        <sql>
            ALTER TABLE material_good ADD COLUMN external_code VARCHAR(20);
        </sql>
    </changeSet>

    <changeSet id="unify-material-goods" author="csaba.birtalan">
        <sql>
            ALTER TABLE material_good DROP COLUMN material_good_type;
        </sql>
    </changeSet>

    <changeSet id="unify-material-goods-2" author="csaba.birtalan">
        <sql>
            ALTER TABLE material_good ADD COLUMN details JSONB;
            UPDATE material_good SET details = common_details || COALESCE(product_details, '{}'::jsonb);
            ALTER TABLE material_good DROP COLUMN common_details, DROP COLUMN product_details;
        </sql>
    </changeSet>

    <changeSet id="unify-material-goods-3" author="csaba.birtalan">
        <sql>
            <![CDATA[
            UPDATE material_good SET details = jsonb_set(details, '{materials}', '[]'::jsonb, true) WHERE NOT (details ?? 'materials');
            ]]>
        </sql>
    </changeSet>

    <changeSet id="add-file-to-company" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS company_file
            (
                create_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                owner_id               UUID      NOT NULL REFERENCES account (id),
                company_id UUID        NOT NULL REFERENCES company (id),
                file_id                UUID      NOT NULL REFERENCES file (id)
                );
        </sql>
    </changeSet>

    <changeSet id="set-19%-vat-for-all-goods" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET details = jsonb_set(details, '{vatRate}', '0.19'::jsonb)
            WHERE details -> 'vatRate' IS NULL;
        </sql>
    </changeSet>
    <changeSet id="set-19%-vat-for-all-goods2" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET details = jsonb_set(details, '{vatRate}', '0.19'::jsonb)
            WHERE details ->> 'vatRate' IS NULL;
        </sql>
    </changeSet>

    <changeSet id="add-renderingdetails-to-purchaseorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE purchase_order ADD COLUMN rendering_details JSONB;
        </sql>
    </changeSet>

    <changeSet id="add-managedby-to-purchaseorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE purchase_order ADD COLUMN managed_by UUID REFERENCES users (id);
        </sql>
    </changeSet>

    <changeSet id="add-wastedquantity-to-old-materialissuenotes" author="csaba.birtalan">
        <sql>
            UPDATE material_issue_note
            SET materials =
                    (SELECT jsonb_agg(jsonb_set(material, '{wastedQuantity}', '0'::jsonb))
                     FROM jsonb_array_elements(materials) AS material);
        </sql>
    </changeSet>

    <changeSet id="change-logo-datatype" author="csaba.birtalan">
        <sql>
            ALTER TABLE account ALTER COLUMN logo TYPE TEXT;
        </sql>
    </changeSet>

    <changeSet id="add-assignedto-to-manufacturingorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order ADD COLUMN assigned_to UUID REFERENCES employee (id);
        </sql>
    </changeSet>

    <changeSet id="set-produced-for-all-goods" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET details = jsonb_set(details, '{produced}', 'false'::jsonb)
            WHERE details ->> 'produced' IS NULL;
        </sql>
    </changeSet>

    <changeSet id="ro-companies-table" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS ro_companies
            (
                create_time    TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                nume           VARCHAR(300),
                cui            VARCHAR(30),
                nr_reg_com     VARCHAR(30),
                judet          VARCHAR(30),
                localitate     VARCHAR(100),
                strada         VARCHAR(100),
                numar          VARCHAR(30),
                sector         VARCHAR(30),
                bloc           VARCHAR(100),
                scara          VARCHAR(30),
                etaj           VARCHAR(30),
                apartament     VARCHAR(30),
                cod_postal     VARCHAR(10),
                text_search    TSVECTOR
                );

            CREATE INDEX ON ro_companies USING GIN (text_search);
        </sql>
    </changeSet>

    <changeSet id="increase-size-of-jobstatus-name" author="csaba.birtalan">
        <sql>
            ALTER TABLE job_status ALTER COLUMN name TYPE varchar(100);
        </sql>
    </changeSet>

    <changeSet id="create-inventory-current-stock" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS inventory_current_stock
            (
                update_time      TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
                material_good_id UUID           NOT NULL REFERENCES material_good (id),
                unit_id          UUID REFERENCES inventory_unit (id),
                quantity         NUMERIC(12, 4) NOT NULL,
                currency         VARCHAR(3) NOT NULL,
                cost             BIGINT NOT NULL,
                total_value      BIGINT NOT NULL,
                UNIQUE (material_good_id, unit_id)
                );
        </sql>
    </changeSet>

    <changeSet id="add-file-to-receptionreceipt" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS receptionreceipt_file
            (
                create_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                owner_id               UUID      NOT NULL REFERENCES account (id),
                reception_receipt_id   UUID      NOT NULL REFERENCES reception_receipt (id),
                file_id                UUID      NOT NULL REFERENCES file (id)
                );
        </sql>
    </changeSet>

    <changeSet id="unify-employees-with-users" author="csaba.birtalan">
        <sql>
            ALTER TABLE users ADD COLUMN details JSONB;

            ALTER TABLE users
                ADD COLUMN text_search tsvector
                    GENERATED ALWAYS AS (to_tsvector('simple', coalesce(name, '') || ' ' || coalesce(details ->> 'position', ''))) STORED;
            CREATE INDEX ON users USING GIN (text_search);

            --drop not null
            ALTER TABLE users ALTER COLUMN external_id DROP NOT NULL;

            INSERT INTO users (id, create_time, update_time, deleted, owner_id, name, details)
            SELECT id, create_time, update_time, deleted, owner_id, name, details
            FROM employee;

            --drop current FKs
            ALTER TABLE device_pairing_token
            DROP CONSTRAINT device_pairing_token_employee_id_fkey;

            ALTER TABLE employee_manufacturingoperationtemplate
            DROP CONSTRAINT employee_manufacturingoperationtemplate_employee_id_fkey;

            ALTER TABLE employee_timeoff
            DROP CONSTRAINT employee_timeoff_employee_id_fkey;

            ALTER TABLE firebase_registration_token
            DROP CONSTRAINT firebase_registration_token_employee_id_fkey;

            ALTER TABLE manufacturingtask_employee
            DROP CONSTRAINT manufacturingtask_employee_employee_id_fkey;

            ALTER TABLE manufacturing_order
            DROP CONSTRAINT manufacturing_order_assigned_to_fkey;

            --rename to user
            ALTER TABLE device_pairing_token
                RENAME COLUMN employee_id TO user_id;

            ALTER TABLE employee_manufacturingoperationtemplate
                RENAME COLUMN employee_id TO user_id;

            ALTER TABLE employee_timeoff
                RENAME COLUMN employee_id TO user_id;

            ALTER TABLE manufacturingtask_employee
                RENAME COLUMN employee_id TO user_id;

            ALTER TABLE manufacturing_order
                RENAME COLUMN assigned_to TO assigned_user_id;

            --recreate FKs
            ALTER TABLE device_pairing_token
                ADD CONSTRAINT device_pairing_token_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);

            ALTER TABLE employee_manufacturingoperationtemplate
                ADD CONSTRAINT employee_manufacturingoperationtemplate_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);

            ALTER TABLE employee_timeoff
                ADD CONSTRAINT employee_timeoff_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);

            ALTER TABLE manufacturingtask_employee
                ADD CONSTRAINT manufacturingtask_employee_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);

            ALTER TABLE manufacturing_order
                ADD CONSTRAINT manufacturing_order_assigned_user_id_fkey FOREIGN KEY (assigned_user_id) REFERENCES users(id);
        </sql>
    </changeSet>

    <changeSet id="unify-employees-with-users-2" author="csaba.birtalan">
        <sql>
            DROP TABLE employee CASCADE;
        </sql>
    </changeSet>

    <changeSet id="unify-employees-with-users-3" author="csaba.birtalan">
        <sql>
            UPDATE users SET details = '{}'::jsonb WHERE details IS NULL;
        </sql>
    </changeSet>

    <changeSet id="isolate-goodsaccompanyingnote-from-salesorder" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS salesorder_goodsaccompanyingnote
            (
                sales_order_id              UUID NOT NULL REFERENCES sales_order (id),
                goods_accompanying_note_id  UUID NOT NULL REFERENCES goods_accompanying_note (id)
                );

            INSERT INTO salesorder_goodsaccompanyingnote (sales_order_id, goods_accompanying_note_id)
            SELECT sales_order_id, id
            FROM goods_accompanying_note;

            ALTER TABLE goods_accompanying_note DROP COLUMN IF EXISTS sales_order_id;
            ALTER TABLE goods_accompanying_note DROP COLUMN IF EXISTS customer_id
        </sql>
    </changeSet>

    <changeSet id="isolate-goodsaccompanyingnote-from-salesorder-2" author="csaba.birtalan">
        <sql>
            ALTER TABLE salesorder_goodsaccompanyingnote ADD CONSTRAINT unique_constraints UNIQUE (sales_order_id, goods_accompanying_note_id);
        </sql>
    </changeSet>

    <changeSet id="goodsaccompanyingnote-for-manufacturingorder" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS manufacturingorder_goodsaccompanyingnote
            (
                manufacturing_order_id     UUID NOT NULL REFERENCES manufacturing_order (id),
                goods_accompanying_note_id UUID NOT NULL REFERENCES goods_accompanying_note (id)
                );

            ALTER TABLE manufacturingorder_goodsaccompanyingnote ADD CONSTRAINT mo_goodsnote_unique_constraints UNIQUE (manufacturing_order_id, goods_accompanying_note_id);
        </sql>
    </changeSet>

    <changeSet id="additional-fields-for-timeoff" author="csaba.birtalan">
        <sql>
            ALTER TABLE employee_timeoff
                ADD COLUMN type VARCHAR(20),
                ADD COLUMN manufacturing_order_id UUID REFERENCES manufacturing_order (id);
        </sql>
    </changeSet>

    <changeSet id="hidden-users" author="csaba.birtalan">
        <sql>
            ALTER TABLE users ADD COLUMN hidden BOOLEAN DEFAULT FALSE;
        </sql>
    </changeSet>

    <changeSet id="type-for-salesorder-file" author="csaba.birtalan">
        <sql>
            ALTER TABLE salesorder_file ADD COLUMN type VARCHAR(20);
        </sql>
    </changeSet>

    <changeSet id="client-signature-for-mo" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order ADD COLUMN base64_client_signature TEXT;
        </sql>
    </changeSet>

    <changeSet id="create-servicing-order" author="csaba.birtalan">
        <sql>
            -- Create the servicing_order table with all required columns
            CREATE TABLE IF NOT EXISTS servicing_order
            (
                id                      UUID PRIMARY KEY,
                create_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                update_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                deleted                BOOLEAN NOT NULL DEFAULT FALSE,
                owner_id               UUID NOT NULL REFERENCES account (id),
                sales_order_id         UUID REFERENCES sales_order (id),
                number                 VARCHAR(20),
                execution_deadline     TIMESTAMP,
                status                 VARCHAR(20),
                quantity               NUMERIC(12, 4),
                ranking                BIGINT,
                notes                  TEXT,
                materials              JSONB,
                operations             JSONB,
                text_search            TSVECTOR,
                service_id             UUID REFERENCES service_template(id),
                servicing_costs        JSONB,
                assigned_to            UUID REFERENCES users(id)
            );

            -- Create the timestamp trigger
            DROP TRIGGER IF EXISTS servicing_order_timestamp ON servicing_order;
            CREATE TRIGGER servicing_order_timestamp
                BEFORE INSERT OR UPDATE
                ON servicing_order
                FOR EACH ROW
            EXECUTE PROCEDURE update_timestamp();

            -- Create text search index
            CREATE INDEX ON servicing_order USING GIN (text_search);

            -- Copy the relevant records from manufacturing_order
            INSERT INTO servicing_order (
                id,
                create_time,
                update_time,
                deleted,
                owner_id,
                sales_order_id,
                number,
                execution_deadline,
                status,
                quantity,
                ranking,
                notes,
                materials,
                operations,
                text_search,
                service_id,
                servicing_costs,
                assigned_to
            )
            SELECT
                id,
                create_time,
                update_time,
                deleted,
                owner_id,
                sales_order_id,
                number,
                production_deadline,
                status,
                quantity,
                ranking,
                notes,
                required_materials,
                manufacturing_operations,
                text_search,
                service_id,
                manufacturing_costs,
                assigned_user_id
            FROM manufacturing_order
            WHERE service_id IS NOT NULL;

            -- First, drop the foreign key constraint
            ALTER TABLE executed_services DROP CONSTRAINT IF EXISTS executed_services_manufacturing_order_id_fkey;

            -- Rename the column
            ALTER TABLE executed_services RENAME COLUMN manufacturing_order_id TO servicing_order_id;

            -- Add the new foreign key constraint
            ALTER TABLE executed_services ADD CONSTRAINT executed_services_servicing_order_id_fkey
                FOREIGN KEY (servicing_order_id) REFERENCES servicing_order (id);

            -- Update the primary key constraint
            ALTER TABLE executed_services DROP CONSTRAINT executed_services_pkey;
            ALTER TABLE executed_services ADD PRIMARY KEY (owner_id, sales_order_id, servicing_order_id, service_id);

            -- delete service order records
            UPDATE manufacturing_order SET deleted = true WHERE service_id IS NOT NULL;

            -- move goods accompanying notes to reference servicing orders
            CREATE TABLE IF NOT EXISTS servicingorder_goodsaccompanyingnote
            (
                servicing_order_id        UUID NOT NULL REFERENCES servicing_order (id),
                goods_accompanying_note_id UUID NOT NULL REFERENCES goods_accompanying_note (id)
                );

            ALTER TABLE servicingorder_goodsaccompanyingnote ADD CONSTRAINT so_goodsnote_unique_constraints UNIQUE (servicing_order_id, goods_accompanying_note_id);

            INSERT INTO servicingorder_goodsaccompanyingnote (servicing_order_id, goods_accompanying_note_id)
            SELECT manufacturing_order_id, goods_accompanying_note_id FROM manufacturingorder_goodsaccompanyingnote;

            DROP TABLE manufacturingorder_goodsaccompanyingnote;
        </sql>
    </changeSet>

    <changeSet id="client-signature-for-servicingorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE servicing_order ADD COLUMN base64_client_signature TEXT;
        </sql>
    </changeSet>

    <changeSet id="change-servicing-order-ranking-to-integer" author="csaba.birtalan">
        <sql>
            -- Change the ranking column type from BIGINT to INTEGER
            ALTER TABLE servicing_order ALTER COLUMN ranking TYPE INTEGER;
        </sql>
    </changeSet>

    <changeSet id="add-file-to-serviceorder" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS servicingorder_file
            (
                create_time        TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                owner_id           UUID      NOT NULL REFERENCES account (id),
                servicing_order_id UUID      NOT NULL REFERENCES servicing_order (id),
                file_id            UUID      NOT NULL REFERENCES file (id)
                );
        </sql>
    </changeSet>

    <changeSet id="add-servicingorder-to-reservedinventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE reserved_inventory ADD COLUMN servicing_order_id UUID REFERENCES servicing_order (id);
        </sql>
    </changeSet>

    <changeSet id="notes-for-servicingorder" author="csaba.birtalan">
        <sql>
            CREATE TABLE IF NOT EXISTS servicing_order_note
            (
                servicing_order_id UUID NOT NULL REFERENCES servicing_order (id),
                note_id            UUID NOT NULL REFERENCES note (id)
                );

            -- move notes to servicing order notes table
            INSERT INTO servicing_order_note (servicing_order_id, note_id)
            SELECT manufacturing_order_id, note_id
            FROM manufacturing_order_note
            WHERE manufacturing_order_id IN (SELECT id FROM manufacturing_order WHERE service_id IS NOT NULL);
        </sql>
    </changeSet>

    <changeSet id="add-serviceorder-to-materialissuenote" author="csaba.birtalan">
        <sql>
            ALTER TABLE material_issue_note ADD COLUMN servicing_order_id UUID REFERENCES servicing_order (id);
        </sql>
    </changeSet>

    <changeSet id="add-serviceorder-to-inventory" author="csaba.birtalan">
        <sql>
            ALTER TABLE inventory ADD COLUMN servicing_order_id UUID REFERENCES servicing_order (id);
        </sql>
    </changeSet>

    <changeSet id="populate-tables-for-serviceorders" author="csaba.birtalan">
        <sql>
            UPDATE reserved_inventory SET servicing_order_id = manufacturing_order_id WHERE manufacturing_order_id IN (SELECT id FROM servicing_order);
            UPDATE material_issue_note SET servicing_order_id = manufacturing_order_id WHERE manufacturing_order_id IN (SELECT id FROM servicing_order);
            UPDATE inventory SET servicing_order_id = manufacturing_order_id WHERE manufacturing_order_id IN (SELECT id FROM servicing_order);
        </sql>
    </changeSet>

    <changeSet id="rename-order-in-employeetimeoff" author="csaba.birtalan">
        <sql>
            ALTER TABLE employee_timeoff RENAME COLUMN manufacturing_order_id TO servicing_order_id;
        </sql>
    </changeSet>

    <changeSet id="update-employee-timeoff-reference" author="csaba.birtalan">
        <sql>
            ALTER TABLE employee_timeoff DROP CONSTRAINT IF EXISTS employee_timeoff_manufacturing_order_id_fkey;
            ALTER TABLE employee_timeoff ADD CONSTRAINT employee_timeoff_servicing_order_id_fkey FOREIGN KEY (servicing_order_id) REFERENCES servicing_order(id);
        </sql>
    </changeSet>

    <changeSet id="cleanup-materialissuenote-for-servicing-orders" author="csaba.birtalan">
        <sql>
            update material_issue_note set manufacturing_order_id = null where servicing_order_id is not null;
        </sql>
    </changeSet>

    <changeSet id="servicingorder-updates" author="csaba.birtalan">
        <sql>
            ALTER TABLE servicing_order ADD COLUMN client_representative VARCHAR(50);
            ALTER TABLE servicing_order ADD COLUMN base64_worker_signature TEXT;
        </sql>
    </changeSet>

    <changeSet id="add-offer-dates-to-so" author="csaba.birtalan">
        <sql>
            ALTER TABLE sales_order ADD COLUMN offer_date TIMESTAMP;
            ALTER TABLE sales_order ADD COLUMN offer_expiration TIMESTAMP;
        </sql>
    </changeSet>

    <changeSet id="add-renderingdetails-to-salesorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE sales_order ADD COLUMN rendering_details JSONB;
        </sql>
    </changeSet>

    <changeSet id="change-delegate-for-goodsaccompanyingnote" author="csaba.birtalan">
        <sql>
            ALTER TABLE goods_accompanying_note DROP COLUMN delegate;
            ALTER TABLE goods_accompanying_note ADD COLUMN delegate_id UUID REFERENCES users (id);
        </sql>
    </changeSet>

    <changeSet id="add-renderingdetails-to-invoice" author="csaba.birtalan">
        <sql>
            ALTER TABLE invoice ADD COLUMN rendering_details JSONB;
        </sql>
    </changeSet>

    <changeSet id="extend-notifications" author="csaba.birtalan">
        <sql>
            ALTER TABLE notification ADD COLUMN target_user_id UUID REFERENCES users (id);
        </sql>
    </changeSet>

    <changeSet id="add-costs-to-services" author="csaba.birtalan">
        <sql>
            ALTER TABLE service_template ADD COLUMN cost_amount INTEGER;
            ALTER TABLE service_template ADD COLUMN cost_currency VARCHAR(3);
        </sql>
    </changeSet>

    <changeSet id="add-customernotes-to-salesorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE sales_order ADD COLUMN customer_notes TEXT;
        </sql>
    </changeSet>

    <changeSet id="move-materials-to-operations-0" author="csaba.birtalan">
            <sql>
                UPDATE material_good
                SET details = jsonb_set(
                        details,
                        '{manufacturingOperations}',
                        '[]'::jsonb
                )
                WHERE details-> 'manufacturingOperations' IS NULL;
            </sql>
    </changeSet>

    <changeSet id="move-materials-to-operations" author="csaba.birtalan">
        <sql>
            UPDATE material_good
            SET details = (
                              details - 'materials'
                              ) || jsonb_build_object(
                                  'manufacturingOperations',
                                  jsonb_set(
                                          details -> 'manufacturingOperations',
                                          '{0,materials}',
                                          details -> 'materials'
                                  )
                                   )
            WHERE jsonb_exists(details, 'materials')
              AND jsonb_exists(details, 'manufacturingOperations')
              AND jsonb_array_length(details->'manufacturingOperations') > 0;

            UPDATE manufacturing_order
            SET manufacturing_operations = jsonb_set(
                    manufacturing_operations,
                    '{0,materials}',
                    required_materials
                                           )
            WHERE required_materials IS NOT NULL
              AND manufacturing_operations IS NOT NULL
              AND jsonb_array_length(manufacturing_operations) > 0;
        </sql>
    </changeSet>

    <!-- TODO uncomment once the above runs -->
    <!--<changeSet id="move-materials-to-operations-2" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order DROP COLUMN required_materials;
        </sql>
    </changeSet>-->

    <changeSet id="move-materials-to-operations-3" author="csaba.birtalan">
        <sql>
            -- For material_good table: Initialize empty materials array for operations 1 and beyond
            UPDATE material_good
            SET details = (
                SELECT jsonb_build_object(
                               'manufacturingOperations',
                               (
                                   SELECT jsonb_agg(
                                                  CASE
                                                      WHEN operation_index = 1 THEN operation_value
                                                      ELSE operation_value || jsonb_build_object('materials', '[]'::jsonb)
                                                      END
                                                      ORDER BY operation_index
                                          )
                                   FROM jsonb_array_elements(details->'manufacturingOperations') WITH ORDINALITY AS t(operation_value, operation_index)
                               )
                       ) || (details - 'manufacturingOperations')
            )
            WHERE jsonb_exists(details, 'manufacturingOperations')
              AND jsonb_array_length(details->'manufacturingOperations') > 1;

            -- For manufacturing_order table: Initialize empty materials array for operations 1 and beyond
            UPDATE manufacturing_order
            SET manufacturing_operations = (
                SELECT jsonb_agg(
                               CASE
                                   WHEN operation_index = 1 THEN operation_value  -- First element (index 1 in WITH ORDINALITY)
                                   ELSE operation_value || jsonb_build_object('materials', '[]'::jsonb)
                                   END
                                   ORDER BY operation_index
                       )
                FROM jsonb_array_elements(manufacturing_operations) WITH ORDINALITY AS t(operation_value, operation_index)
            )
            WHERE manufacturing_operations IS NOT NULL
              AND jsonb_array_length(manufacturing_operations) > 1;
        </sql>
    </changeSet>

    <changeSet id="move-materials-to-operations-4" author="csaba.birtalan">
        <sql>
            UPDATE servicing_order
            SET
                operations = (
                    SELECT jsonb_agg(
                                   value || jsonb_build_object('materials', '[]'::jsonb)
                           )
                    FROM jsonb_array_elements(operations) AS value
                )
            WHERE operations IS NOT NULL AND jsonb_array_length(operations) > 0;
        </sql>
    </changeSet>

    <changeSet id="add-parent-to-manufacturingorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE manufacturing_order ADD COLUMN parent_id UUID REFERENCES manufacturing_order (id);
        </sql>
    </changeSet>

    <changeSet id="change-done-to-consumption-recorded-for-manufacturingorder" author="csaba.birtalan">
        <sql>
            UPDATE manufacturing_order SET status = 'CONSUMPTION_RECORDED' WHERE status = 'DONE';
        </sql>
    </changeSet>

    <changeSet id="change-servicingorder-statuses" author="csaba.birtalan">
        <sql>
            UPDATE servicing_order SET status = 'IN_PROGRESS' WHERE status = 'MANUFACTURING';
            UPDATE servicing_order SET status = 'EXECUTED' WHERE status = 'MANUFACTURED';
            UPDATE servicing_order SET status = 'CLOSED' WHERE status = 'DONE';
        </sql>
    </changeSet>

    <changeSet id="add-blockedreason-to-servicingorder" author="csaba.birtalan">
        <sql>
            ALTER TABLE servicing_order ADD COLUMN blocked_reason VARCHAR(20);
        </sql>
    </changeSet>

    <!--    <changeSet id="add-oauth-tokens" author="csaba.birtalan">
            <sql>
                CREATE TABLE IF NOT EXISTS oauth_token
                (
                    id            UUID PRIMARY KEY,
                    create_time   TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    owner_id      UUID      NOT NULL REFERENCES account (id),
                    user_id       UUID REFERENCES users (id),
                    service       VARCHAR(50),
                    token         VARCHAR(5000),
                    refresh_token VARCHAR(5000),
                    scope         VARCHAR(500),
                    expires_in    INTEGER
                    );
            </sql>
        </changeSet>-->

</databaseChangeLog>