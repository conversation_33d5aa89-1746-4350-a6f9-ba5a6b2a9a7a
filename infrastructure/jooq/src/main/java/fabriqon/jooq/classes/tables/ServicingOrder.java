/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Indexes;
import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ServicingOrderRecord;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServicingOrder extends TableImpl<ServicingOrderRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.servicing_order</code>
     */
    public static final ServicingOrder SERVICING_ORDER = new ServicingOrder();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ServicingOrderRecord> getRecordType() {
        return ServicingOrderRecord.class;
    }

    /**
     * The column <code>public.servicing_order.id</code>.
     */
    public final TableField<ServicingOrderRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.servicing_order.create_time</code>.
     */
    public final TableField<ServicingOrderRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.servicing_order.update_time</code>.
     */
    public final TableField<ServicingOrderRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.servicing_order.deleted</code>.
     */
    public final TableField<ServicingOrderRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.servicing_order.owner_id</code>.
     */
    public final TableField<ServicingOrderRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.servicing_order.sales_order_id</code>.
     */
    public final TableField<ServicingOrderRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.servicing_order.number</code>.
     */
    public final TableField<ServicingOrderRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.servicing_order.execution_deadline</code>.
     */
    public final TableField<ServicingOrderRecord, LocalDateTime> EXECUTION_DEADLINE = createField(DSL.name("execution_deadline"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.servicing_order.status</code>.
     */
    public final TableField<ServicingOrderRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.servicing_order.quantity</code>.
     */
    public final TableField<ServicingOrderRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC(12, 4), this, "");

    /**
     * The column <code>public.servicing_order.ranking</code>.
     */
    public final TableField<ServicingOrderRecord, Integer> RANKING = createField(DSL.name("ranking"), SQLDataType.INTEGER, this, "");

    /**
     * The column <code>public.servicing_order.notes</code>.
     */
    public final TableField<ServicingOrderRecord, String> NOTES = createField(DSL.name("notes"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>public.servicing_order.materials</code>.
     */
    public final TableField<ServicingOrderRecord, JSONB> MATERIALS = createField(DSL.name("materials"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.servicing_order.operations</code>.
     */
    public final TableField<ServicingOrderRecord, JSONB> OPERATIONS = createField(DSL.name("operations"), SQLDataType.JSONB, this, "");

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public final TableField<ServicingOrderRecord, Object> TEXT_SEARCH = createField(DSL.name("text_search"), org.jooq.impl.DefaultDataType.getDefaultDataType("\"pg_catalog\".\"tsvector\""), this, "");

    /**
     * The column <code>public.servicing_order.service_id</code>.
     */
    public final TableField<ServicingOrderRecord, UUID> SERVICE_ID = createField(DSL.name("service_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.servicing_order.servicing_costs</code>.
     */
    public final TableField<ServicingOrderRecord, JSONB> SERVICING_COSTS = createField(DSL.name("servicing_costs"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.servicing_order.assigned_to</code>.
     */
    public final TableField<ServicingOrderRecord, UUID> ASSIGNED_TO = createField(DSL.name("assigned_to"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.servicing_order.base64_client_signature</code>.
     */
    public final TableField<ServicingOrderRecord, String> BASE64_CLIENT_SIGNATURE = createField(DSL.name("base64_client_signature"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>public.servicing_order.client_representative</code>.
     */
    public final TableField<ServicingOrderRecord, String> CLIENT_REPRESENTATIVE = createField(DSL.name("client_representative"), SQLDataType.VARCHAR(50), this, "");

    /**
     * The column <code>public.servicing_order.base64_worker_signature</code>.
     */
    public final TableField<ServicingOrderRecord, String> BASE64_WORKER_SIGNATURE = createField(DSL.name("base64_worker_signature"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>public.servicing_order.blocked_reason</code>.
     */
    public final TableField<ServicingOrderRecord, String> BLOCKED_REASON = createField(DSL.name("blocked_reason"), SQLDataType.VARCHAR(20), this, "");

    private ServicingOrder(Name alias, Table<ServicingOrderRecord> aliased) {
        this(alias, aliased, null);
    }

    private ServicingOrder(Name alias, Table<ServicingOrderRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.servicing_order</code> table reference
     */
    public ServicingOrder(String alias) {
        this(DSL.name(alias), SERVICING_ORDER);
    }

    /**
     * Create an aliased <code>public.servicing_order</code> table reference
     */
    public ServicingOrder(Name alias) {
        this(alias, SERVICING_ORDER);
    }

    /**
     * Create a <code>public.servicing_order</code> table reference
     */
    public ServicingOrder() {
        this(DSL.name("servicing_order"), null);
    }

    public <O extends Record> ServicingOrder(Table<O> child, ForeignKey<O, ServicingOrderRecord> key) {
        super(child, key, SERVICING_ORDER);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.SERVICING_ORDER_TEXT_SEARCH_IDX);
    }

    @Override
    public UniqueKey<ServicingOrderRecord> getPrimaryKey() {
        return Keys.SERVICING_ORDER_PKEY;
    }

    @Override
    public List<ForeignKey<ServicingOrderRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SERVICING_ORDER__SERVICING_ORDER_OWNER_ID_FKEY, Keys.SERVICING_ORDER__SERVICING_ORDER_SALES_ORDER_ID_FKEY, Keys.SERVICING_ORDER__SERVICING_ORDER_SERVICE_ID_FKEY, Keys.SERVICING_ORDER__SERVICING_ORDER_ASSIGNED_TO_FKEY);
    }

    private transient Account _account;
    private transient SalesOrder _salesOrder;
    private transient ServiceTemplate _serviceTemplate;
    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.SERVICING_ORDER__SERVICING_ORDER_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.SERVICING_ORDER__SERVICING_ORDER_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the <code>public.service_template</code>
     * table.
     */
    public ServiceTemplate serviceTemplate() {
        if (_serviceTemplate == null)
            _serviceTemplate = new ServiceTemplate(this, Keys.SERVICING_ORDER__SERVICING_ORDER_SERVICE_ID_FKEY);

        return _serviceTemplate;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.SERVICING_ORDER__SERVICING_ORDER_ASSIGNED_TO_FKEY);

        return _users;
    }

    @Override
    public ServicingOrder as(String alias) {
        return new ServicingOrder(DSL.name(alias), this);
    }

    @Override
    public ServicingOrder as(Name alias) {
        return new ServicingOrder(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ServicingOrder rename(String name) {
        return new ServicingOrder(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ServicingOrder rename(Name name) {
        return new ServicingOrder(name, null);
    }

    // -------------------------------------------------------------------------
    // Row22 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row22<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, String, BigDecimal, Integer, String, JSONB, JSONB, Object, UUID, JSONB, UUID, String, String, String, String> fieldsRow() {
        return (Row22) super.fieldsRow();
    }
}
