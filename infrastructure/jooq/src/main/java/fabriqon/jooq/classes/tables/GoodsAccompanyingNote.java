/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.GoodsAccompanyingNoteRecord;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GoodsAccompanyingNote extends TableImpl<GoodsAccompanyingNoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.goods_accompanying_note</code>
     */
    public static final GoodsAccompanyingNote GOODS_ACCOMPANYING_NOTE = new GoodsAccompanyingNote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GoodsAccompanyingNoteRecord> getRecordType() {
        return GoodsAccompanyingNoteRecord.class;
    }

    /**
     * The column <code>public.goods_accompanying_note.id</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.goods_accompanying_note.create_time</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.goods_accompanying_note.update_time</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.goods_accompanying_note.deleted</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.goods_accompanying_note.owner_id</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.goods_accompanying_note.number</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(200).nullable(false), this, "");

    /**
     * The column <code>public.goods_accompanying_note.delivery_date</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, LocalDateTime> DELIVERY_DATE = createField(DSL.name("delivery_date"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>public.goods_accompanying_note.from_address</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, JSONB> FROM_ADDRESS = createField(DSL.name("from_address"), SQLDataType.JSONB.nullable(false), this, "");

    /**
     * The column <code>public.goods_accompanying_note.to_address</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, JSONB> TO_ADDRESS = createField(DSL.name("to_address"), SQLDataType.JSONB.nullable(false), this, "");

    /**
     * The column
     * <code>public.goods_accompanying_note.transport_registration_number</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, String> TRANSPORT_REGISTRATION_NUMBER = createField(DSL.name("transport_registration_number"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.goods_accompanying_note.notes</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, String> NOTES = createField(DSL.name("notes"), SQLDataType.VARCHAR(500), this, "");

    /**
     * The column <code>public.goods_accompanying_note.items</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, JSONB> ITEMS = createField(DSL.name("items"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.goods_accompanying_note.delegate_id</code>.
     */
    public final TableField<GoodsAccompanyingNoteRecord, UUID> DELEGATE_ID = createField(DSL.name("delegate_id"), SQLDataType.UUID, this, "");

    private GoodsAccompanyingNote(Name alias, Table<GoodsAccompanyingNoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private GoodsAccompanyingNote(Name alias, Table<GoodsAccompanyingNoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.goods_accompanying_note</code> table
     * reference
     */
    public GoodsAccompanyingNote(String alias) {
        this(DSL.name(alias), GOODS_ACCOMPANYING_NOTE);
    }

    /**
     * Create an aliased <code>public.goods_accompanying_note</code> table
     * reference
     */
    public GoodsAccompanyingNote(Name alias) {
        this(alias, GOODS_ACCOMPANYING_NOTE);
    }

    /**
     * Create a <code>public.goods_accompanying_note</code> table reference
     */
    public GoodsAccompanyingNote() {
        this(DSL.name("goods_accompanying_note"), null);
    }

    public <O extends Record> GoodsAccompanyingNote(Table<O> child, ForeignKey<O, GoodsAccompanyingNoteRecord> key) {
        super(child, key, GOODS_ACCOMPANYING_NOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<GoodsAccompanyingNoteRecord> getPrimaryKey() {
        return Keys.GOODS_ACCOMPANYING_NOTE_PKEY;
    }

    @Override
    public List<ForeignKey<GoodsAccompanyingNoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.GOODS_ACCOMPANYING_NOTE__GOODS_ACCOMPANYING_NOTE_OWNER_ID_FKEY, Keys.GOODS_ACCOMPANYING_NOTE__GOODS_ACCOMPANYING_NOTE_DELEGATE_ID_FKEY);
    }

    private transient Account _account;
    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.GOODS_ACCOMPANYING_NOTE__GOODS_ACCOMPANYING_NOTE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.GOODS_ACCOMPANYING_NOTE__GOODS_ACCOMPANYING_NOTE_DELEGATE_ID_FKEY);

        return _users;
    }

    @Override
    public GoodsAccompanyingNote as(String alias) {
        return new GoodsAccompanyingNote(DSL.name(alias), this);
    }

    @Override
    public GoodsAccompanyingNote as(Name alias) {
        return new GoodsAccompanyingNote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GoodsAccompanyingNote rename(String name) {
        return new GoodsAccompanyingNote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public GoodsAccompanyingNote rename(Name name) {
        return new GoodsAccompanyingNote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row13 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row13<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, LocalDateTime, JSONB, JSONB, String, String, JSONB, UUID> fieldsRow() {
        return (Row13) super.fieldsRow();
    }
}
