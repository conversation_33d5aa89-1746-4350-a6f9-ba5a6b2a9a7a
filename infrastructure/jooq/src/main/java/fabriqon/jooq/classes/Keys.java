/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes;


import fabriqon.jooq.classes.tables.*;
import fabriqon.jooq.classes.tables.records.*;
import org.jooq.ForeignKey;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling foreign key relationships and constraints of tables in
 * public.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<AccountRecord> ACCOUNT_PKEY = Internal.createUniqueKey(Account.ACCOUNT, DSL.name("account_pkey"), new TableField[] { Account.ACCOUNT.ID }, true);
    public static final UniqueKey<CategoryRecord> CATEGORY_PKEY = Internal.createUniqueKey(Category.CATEGORY, DSL.name("category_pkey"), new TableField[] { Category.CATEGORY.ID }, true);
    public static final UniqueKey<CompanyRecord> COMPANY_PKEY = Internal.createUniqueKey(Company.COMPANY, DSL.name("company_pkey"), new TableField[] { Company.COMPANY.ID }, true);
    public static final UniqueKey<DatabasechangeloglockRecord> DATABASECHANGELOGLOCK_PKEY = Internal.createUniqueKey(Databasechangeloglock.DATABASECHANGELOGLOCK, DSL.name("databasechangeloglock_pkey"), new TableField[] { Databasechangeloglock.DATABASECHANGELOGLOCK.ID }, true);
    public static final UniqueKey<EmployeeTimeoffRecord> EMPLOYEE_TIMEOFF_PKEY = Internal.createUniqueKey(EmployeeTimeoff.EMPLOYEE_TIMEOFF, DSL.name("employee_timeoff_pkey"), new TableField[] { EmployeeTimeoff.EMPLOYEE_TIMEOFF.ID }, true);
    public static final UniqueKey<ExecutedServicesRecord> EXECUTED_SERVICES_PKEY = Internal.createUniqueKey(ExecutedServices.EXECUTED_SERVICES, DSL.name("executed_services_pkey"), new TableField[] { ExecutedServices.EXECUTED_SERVICES.OWNER_ID, ExecutedServices.EXECUTED_SERVICES.SALES_ORDER_ID, ExecutedServices.EXECUTED_SERVICES.SERVICING_ORDER_ID, ExecutedServices.EXECUTED_SERVICES.SERVICE_ID }, true);
    public static final UniqueKey<FileRecord> FILE_PKEY = Internal.createUniqueKey(File.FILE, DSL.name("file_pkey"), new TableField[] { File.FILE.ID }, true);
    public static final UniqueKey<FirebaseRegistrationTokenRecord> FIREBASE_REGISTRATION_TOKEN_EMPLOYEE_ID_KEY = Internal.createUniqueKey(FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN, DSL.name("firebase_registration_token_employee_id_key"), new TableField[] { FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.EMPLOYEE_ID }, true);
    public static final UniqueKey<FirebaseRegistrationTokenRecord> FIREBASE_REGISTRATION_TOKEN_PKEY = Internal.createUniqueKey(FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN, DSL.name("firebase_registration_token_pkey"), new TableField[] { FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.ID }, true);
    public static final UniqueKey<FirebaseRegistrationTokenRecord> FIREBASE_REGISTRATION_TOKEN_USER_ID_KEY = Internal.createUniqueKey(FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN, DSL.name("firebase_registration_token_user_id_key"), new TableField[] { FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.USER_ID }, true);
    public static final UniqueKey<GoodsAccompanyingNoteRecord> GOODS_ACCOMPANYING_NOTE_PKEY = Internal.createUniqueKey(GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE, DSL.name("goods_accompanying_note_pkey"), new TableField[] { GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.ID }, true);
    public static final UniqueKey<InventoryRecord> INVENTORY_PKEY = Internal.createUniqueKey(Inventory.INVENTORY, DSL.name("inventory_pkey"), new TableField[] { Inventory.INVENTORY.ID }, true);
    public static final UniqueKey<InventoryAdjustmentOrderRecord> INVENTORY_ADJUSTMENT_ORDER_PKEY = Internal.createUniqueKey(InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER, DSL.name("inventory_adjustment_order_pkey"), new TableField[] { InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.ID }, true);
    public static final UniqueKey<InventoryCurrentStockRecord> INVENTORY_CURRENT_STOCK_MATERIAL_GOOD_ID_UNIT_ID_KEY = Internal.createUniqueKey(InventoryCurrentStock.INVENTORY_CURRENT_STOCK, DSL.name("inventory_current_stock_material_good_id_unit_id_key"), new TableField[] { InventoryCurrentStock.INVENTORY_CURRENT_STOCK.MATERIAL_GOOD_ID, InventoryCurrentStock.INVENTORY_CURRENT_STOCK.UNIT_ID }, true);
    public static final UniqueKey<InventoryUnitRecord> INVENTORY_TYPE_PKEY = Internal.createUniqueKey(InventoryUnit.INVENTORY_UNIT, DSL.name("inventory_type_pkey"), new TableField[] { InventoryUnit.INVENTORY_UNIT.ID }, true);
    public static final UniqueKey<InvoiceRecord> INVOICE_PKEY = Internal.createUniqueKey(Invoice.INVOICE, DSL.name("invoice_pkey"), new TableField[] { Invoice.INVOICE.ID }, true);
    public static final UniqueKey<InvoiceRecord> INVOICE_SALES_ORDER_ID_PROFORMA_KEY = Internal.createUniqueKey(Invoice.INVOICE, DSL.name("invoice_sales_order_id_proforma_key"), new TableField[] { Invoice.INVOICE.SALES_ORDER_ID, Invoice.INVOICE.PROFORMA }, true);
    public static final UniqueKey<LocationRecord> LOCATION_PKEY = Internal.createUniqueKey(Location.LOCATION, DSL.name("location_pkey"), new TableField[] { Location.LOCATION.ID }, true);
    public static final UniqueKey<ManufacturingOperationTemplateRecord> MANUFACTURING_OPERATION_TEMPLATE_PKEY = Internal.createUniqueKey(ManufacturingOperationTemplate.MANUFACTURING_OPERATION_TEMPLATE, DSL.name("manufacturing_operation_template_pkey"), new TableField[] { ManufacturingOperationTemplate.MANUFACTURING_OPERATION_TEMPLATE.ID }, true);
    public static final UniqueKey<ManufacturingOrderRecord> MANUFACTURING_ORDER_PKEY = Internal.createUniqueKey(ManufacturingOrder.MANUFACTURING_ORDER, DSL.name("manufacturing_order_pkey"), new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ID }, true);
    public static final UniqueKey<ManufacturingTaskRecord> MANUFACTURING_TASK_PKEY = Internal.createUniqueKey(ManufacturingTask.MANUFACTURING_TASK, DSL.name("manufacturing_task_pkey"), new TableField[] { ManufacturingTask.MANUFACTURING_TASK.ID }, true);
    public static final UniqueKey<ManufacturingWorkstationRecord> MANUFACTURING_WORKSTATION_PKEY = Internal.createUniqueKey(ManufacturingWorkstation.MANUFACTURING_WORKSTATION, DSL.name("manufacturing_workstation_pkey"), new TableField[] { ManufacturingWorkstation.MANUFACTURING_WORKSTATION.ID }, true);
    public static final UniqueKey<MaterialGoodRecord> MATERIAL_GOOD_PKEY = Internal.createUniqueKey(MaterialGood.MATERIAL_GOOD, DSL.name("material_good_pkey"), new TableField[] { MaterialGood.MATERIAL_GOOD.ID }, true);
    public static final UniqueKey<MaterialIssueNoteRecord> MATERIAL_ISSUE_NOTE_PKEY = Internal.createUniqueKey(MaterialIssueNote.MATERIAL_ISSUE_NOTE, DSL.name("material_issue_note_pkey"), new TableField[] { MaterialIssueNote.MATERIAL_ISSUE_NOTE.ID }, true);
    public static final UniqueKey<NoteRecord> NOTE_PKEY = Internal.createUniqueKey(Note.NOTE, DSL.name("note_pkey"), new TableField[] { Note.NOTE.ID }, true);
    public static final UniqueKey<NotificationRecord> NOTIFICATION_PKEY = Internal.createUniqueKey(Notification.NOTIFICATION, DSL.name("notification_pkey"), new TableField[] { Notification.NOTIFICATION.ID }, true);
    public static final UniqueKey<NotificationReadByRecord> NOTIFICATION_READ_BY_NOTIFICATION_ID_USER_ID_KEY = Internal.createUniqueKey(NotificationReadBy.NOTIFICATION_READ_BY, DSL.name("notification_read_by_notification_id_user_id_key"), new TableField[] { NotificationReadBy.NOTIFICATION_READ_BY.NOTIFICATION_ID, NotificationReadBy.NOTIFICATION_READ_BY.USER_ID }, true);
    public static final UniqueKey<PurchaseOrderRecord> PURCHASE_ORDER_PKEY = Internal.createUniqueKey(PurchaseOrder.PURCHASE_ORDER, DSL.name("purchase_order_pkey"), new TableField[] { PurchaseOrder.PURCHASE_ORDER.ID }, true);
    public static final UniqueKey<PurchaseWishlistRecord> PURCHASE_WISHLIST_OWNER_ID_MATERIAL_GOOD_ID_SUPPLIER_ID_KEY = Internal.createUniqueKey(PurchaseWishlist.PURCHASE_WISHLIST, DSL.name("purchase_wishlist_owner_id_material_good_id_supplier_id_key"), new TableField[] { PurchaseWishlist.PURCHASE_WISHLIST.OWNER_ID, PurchaseWishlist.PURCHASE_WISHLIST.MATERIAL_GOOD_ID, PurchaseWishlist.PURCHASE_WISHLIST.SUPPLIER_ID }, true);
    public static final UniqueKey<PurchaseWishlistRecord> PURCHASE_WISHLIST_PKEY = Internal.createUniqueKey(PurchaseWishlist.PURCHASE_WISHLIST, DSL.name("purchase_wishlist_pkey"), new TableField[] { PurchaseWishlist.PURCHASE_WISHLIST.ID }, true);
    public static final UniqueKey<ReceptionReceiptRecord> RECEPTION_RECEIPT_PKEY = Internal.createUniqueKey(ReceptionReceipt.RECEPTION_RECEIPT, DSL.name("reception_receipt_pkey"), new TableField[] { ReceptionReceipt.RECEPTION_RECEIPT.ID }, true);
    public static final UniqueKey<ReservedInventoryRecord> RESERVED_INVENTORY_PKEY = Internal.createUniqueKey(ReservedInventory.RESERVED_INVENTORY, DSL.name("reserved_inventory_pkey"), new TableField[] { ReservedInventory.RESERVED_INVENTORY.ID }, true);
    public static final UniqueKey<SalesOrderRecord> SALES_ORDER_PKEY = Internal.createUniqueKey(SalesOrder.SALES_ORDER, DSL.name("sales_order_pkey"), new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final UniqueKey<SalesorderGoodsaccompanyingnoteRecord> UNIQUE_CONSTRAINTS = Internal.createUniqueKey(SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE, DSL.name("unique_constraints"), new TableField[] { SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID, SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID }, true);
    public static final UniqueKey<SequenceRecord> SEQUENCE_PKEY = Internal.createUniqueKey(Sequence.SEQUENCE, DSL.name("sequence_pkey"), new TableField[] { Sequence.SEQUENCE.ID }, true);
    public static final UniqueKey<ServiceTemplateRecord> SERVICE_TEMPLATE_PKEY = Internal.createUniqueKey(ServiceTemplate.SERVICE_TEMPLATE, DSL.name("service_template_pkey"), new TableField[] { ServiceTemplate.SERVICE_TEMPLATE.ID }, true);
    public static final UniqueKey<ServicingOrderRecord> SERVICING_ORDER_PKEY = Internal.createUniqueKey(ServicingOrder.SERVICING_ORDER, DSL.name("servicing_order_pkey"), new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final UniqueKey<ServicingorderGoodsaccompanyingnoteRecord> SO_GOODSNOTE_UNIQUE_CONSTRAINTS = Internal.createUniqueKey(ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE, DSL.name("so_goodsnote_unique_constraints"), new TableField[] { ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE.SERVICING_ORDER_ID, ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID }, true);
    public static final UniqueKey<SystemEventRecord> SYSTEM_EVENT_PKEY = Internal.createUniqueKey(SystemEvent.SYSTEM_EVENT, DSL.name("system_event_pkey"), new TableField[] { SystemEvent.SYSTEM_EVENT.ID }, true);
    public static final UniqueKey<UsersRecord> USERS_EXTERNAL_ID_KEY = Internal.createUniqueKey(Users.USERS, DSL.name("users_external_id_key"), new TableField[] { Users.USERS.EXTERNAL_ID }, true);
    public static final UniqueKey<UsersRecord> USERS_PKEY = Internal.createUniqueKey(Users.USERS, DSL.name("users_pkey"), new TableField[] { Users.USERS.ID }, true);

    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------

    public static final ForeignKey<AccountCustomerRecord, CompanyRecord> ACCOUNT_CUSTOMER__ACCOUNT_CUSTOMER_CUSTOMER_ID_FKEY = Internal.createForeignKey(AccountCustomer.ACCOUNT_CUSTOMER, DSL.name("account_customer_customer_id_fkey"), new TableField[] { AccountCustomer.ACCOUNT_CUSTOMER.CUSTOMER_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<AccountCustomerRecord, AccountRecord> ACCOUNT_CUSTOMER__ACCOUNT_CUSTOMER_OWNER_ID_FKEY = Internal.createForeignKey(AccountCustomer.ACCOUNT_CUSTOMER, DSL.name("account_customer_owner_id_fkey"), new TableField[] { AccountCustomer.ACCOUNT_CUSTOMER.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<AccountSupplierRecord, AccountRecord> ACCOUNT_SUPPLIER__ACCOUNT_SUPPLIER_OWNER_ID_FKEY = Internal.createForeignKey(AccountSupplier.ACCOUNT_SUPPLIER, DSL.name("account_supplier_owner_id_fkey"), new TableField[] { AccountSupplier.ACCOUNT_SUPPLIER.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<AccountSupplierRecord, CompanyRecord> ACCOUNT_SUPPLIER__ACCOUNT_SUPPLIER_SUPPLIER_ID_FKEY = Internal.createForeignKey(AccountSupplier.ACCOUNT_SUPPLIER, DSL.name("account_supplier_supplier_id_fkey"), new TableField[] { AccountSupplier.ACCOUNT_SUPPLIER.SUPPLIER_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<CategoryRecord, AccountRecord> CATEGORY__CATEGORY_OWNER_ID_FKEY = Internal.createForeignKey(Category.CATEGORY, DSL.name("category_owner_id_fkey"), new TableField[] { Category.CATEGORY.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<CompanyRecord, AccountRecord> COMPANY__COMPANY_OWNER_ID_FKEY = Internal.createForeignKey(Company.COMPANY, DSL.name("company_owner_id_fkey"), new TableField[] { Company.COMPANY.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<CompanyFileRecord, CompanyRecord> COMPANY_FILE__COMPANY_FILE_COMPANY_ID_FKEY = Internal.createForeignKey(CompanyFile.COMPANY_FILE, DSL.name("company_file_company_id_fkey"), new TableField[] { CompanyFile.COMPANY_FILE.COMPANY_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<CompanyFileRecord, FileRecord> COMPANY_FILE__COMPANY_FILE_FILE_ID_FKEY = Internal.createForeignKey(CompanyFile.COMPANY_FILE, DSL.name("company_file_file_id_fkey"), new TableField[] { CompanyFile.COMPANY_FILE.FILE_ID }, Keys.FILE_PKEY, new TableField[] { File.FILE.ID }, true);
    public static final ForeignKey<CompanyFileRecord, AccountRecord> COMPANY_FILE__COMPANY_FILE_OWNER_ID_FKEY = Internal.createForeignKey(CompanyFile.COMPANY_FILE, DSL.name("company_file_owner_id_fkey"), new TableField[] { CompanyFile.COMPANY_FILE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<CustomerNoteRecord, CompanyRecord> CUSTOMER_NOTE__CUSTOMER_NOTE_COMPANY_ID_FKEY = Internal.createForeignKey(CustomerNote.CUSTOMER_NOTE, DSL.name("customer_note_company_id_fkey"), new TableField[] { CustomerNote.CUSTOMER_NOTE.COMPANY_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<CustomerNoteRecord, NoteRecord> CUSTOMER_NOTE__CUSTOMER_NOTE_NOTE_ID_FKEY = Internal.createForeignKey(CustomerNote.CUSTOMER_NOTE, DSL.name("customer_note_note_id_fkey"), new TableField[] { CustomerNote.CUSTOMER_NOTE.NOTE_ID }, Keys.NOTE_PKEY, new TableField[] { Note.NOTE.ID }, true);
    public static final ForeignKey<DevicePairingTokenRecord, UsersRecord> DEVICE_PAIRING_TOKEN__DEVICE_PAIRING_TOKEN_USER_ID_FKEY = Internal.createForeignKey(DevicePairingToken.DEVICE_PAIRING_TOKEN, DSL.name("device_pairing_token_user_id_fkey"), new TableField[] { DevicePairingToken.DEVICE_PAIRING_TOKEN.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<EmployeeManufacturingoperationtemplateRecord, ManufacturingOperationTemplateRecord> EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATI_MANUFACTURING_OPERATION_TEMP_FKEY = Internal.createForeignKey(EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE, DSL.name("employee_manufacturingoperati_manufacturing_operation_temp_fkey"), new TableField[] { EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID }, Keys.MANUFACTURING_OPERATION_TEMPLATE_PKEY, new TableField[] { ManufacturingOperationTemplate.MANUFACTURING_OPERATION_TEMPLATE.ID }, true);
    public static final ForeignKey<EmployeeManufacturingoperationtemplateRecord, AccountRecord> EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE_OWNER_ID_FKEY = Internal.createForeignKey(EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE, DSL.name("employee_manufacturingoperationtemplate_owner_id_fkey"), new TableField[] { EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<EmployeeManufacturingoperationtemplateRecord, UsersRecord> EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE_USER_ID_FKEY = Internal.createForeignKey(EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE, DSL.name("employee_manufacturingoperationtemplate_user_id_fkey"), new TableField[] { EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<EmployeeTimeoffRecord, AccountRecord> EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_OWNER_ID_FKEY = Internal.createForeignKey(EmployeeTimeoff.EMPLOYEE_TIMEOFF, DSL.name("employee_timeoff_owner_id_fkey"), new TableField[] { EmployeeTimeoff.EMPLOYEE_TIMEOFF.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<EmployeeTimeoffRecord, ServicingOrderRecord> EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_SERVICING_ORDER_ID_FKEY = Internal.createForeignKey(EmployeeTimeoff.EMPLOYEE_TIMEOFF, DSL.name("employee_timeoff_servicing_order_id_fkey"), new TableField[] { EmployeeTimeoff.EMPLOYEE_TIMEOFF.SERVICING_ORDER_ID }, Keys.SERVICING_ORDER_PKEY, new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final ForeignKey<EmployeeTimeoffRecord, UsersRecord> EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_USER_ID_FKEY = Internal.createForeignKey(EmployeeTimeoff.EMPLOYEE_TIMEOFF, DSL.name("employee_timeoff_user_id_fkey"), new TableField[] { EmployeeTimeoff.EMPLOYEE_TIMEOFF.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<ExecutedServicesRecord, AccountRecord> EXECUTED_SERVICES__EXECUTED_SERVICES_OWNER_ID_FKEY = Internal.createForeignKey(ExecutedServices.EXECUTED_SERVICES, DSL.name("executed_services_owner_id_fkey"), new TableField[] { ExecutedServices.EXECUTED_SERVICES.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ExecutedServicesRecord, SalesOrderRecord> EXECUTED_SERVICES__EXECUTED_SERVICES_SALES_ORDER_ID_FKEY = Internal.createForeignKey(ExecutedServices.EXECUTED_SERVICES, DSL.name("executed_services_sales_order_id_fkey"), new TableField[] { ExecutedServices.EXECUTED_SERVICES.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<ExecutedServicesRecord, ServiceTemplateRecord> EXECUTED_SERVICES__EXECUTED_SERVICES_SERVICE_ID_FKEY = Internal.createForeignKey(ExecutedServices.EXECUTED_SERVICES, DSL.name("executed_services_service_id_fkey"), new TableField[] { ExecutedServices.EXECUTED_SERVICES.SERVICE_ID }, Keys.SERVICE_TEMPLATE_PKEY, new TableField[] { ServiceTemplate.SERVICE_TEMPLATE.ID }, true);
    public static final ForeignKey<ExecutedServicesRecord, ServicingOrderRecord> EXECUTED_SERVICES__EXECUTED_SERVICES_SERVICING_ORDER_ID_FKEY = Internal.createForeignKey(ExecutedServices.EXECUTED_SERVICES, DSL.name("executed_services_servicing_order_id_fkey"), new TableField[] { ExecutedServices.EXECUTED_SERVICES.SERVICING_ORDER_ID }, Keys.SERVICING_ORDER_PKEY, new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final ForeignKey<FileRecord, AccountRecord> FILE__FILE_OWNER_ID_FKEY = Internal.createForeignKey(File.FILE, DSL.name("file_owner_id_fkey"), new TableField[] { File.FILE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<FirebaseRegistrationTokenRecord, AccountRecord> FIREBASE_REGISTRATION_TOKEN__FIREBASE_REGISTRATION_TOKEN_OWNER_ID_FKEY = Internal.createForeignKey(FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN, DSL.name("firebase_registration_token_owner_id_fkey"), new TableField[] { FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<FirebaseRegistrationTokenRecord, UsersRecord> FIREBASE_REGISTRATION_TOKEN__FIREBASE_REGISTRATION_TOKEN_USER_ID_FKEY = Internal.createForeignKey(FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN, DSL.name("firebase_registration_token_user_id_fkey"), new TableField[] { FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<GoodsAccompanyingNoteRecord, UsersRecord> GOODS_ACCOMPANYING_NOTE__GOODS_ACCOMPANYING_NOTE_DELEGATE_ID_FKEY = Internal.createForeignKey(GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE, DSL.name("goods_accompanying_note_delegate_id_fkey"), new TableField[] { GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.DELEGATE_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<GoodsAccompanyingNoteRecord, AccountRecord> GOODS_ACCOMPANYING_NOTE__GOODS_ACCOMPANYING_NOTE_OWNER_ID_FKEY = Internal.createForeignKey(GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE, DSL.name("goods_accompanying_note_owner_id_fkey"), new TableField[] { GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<InventoryRecord, InventoryAdjustmentOrderRecord> INVENTORY__INVENTORY_INVENTORY_ADJUSTMENT_ORDER_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_inventory_adjustment_order_id_fkey"), new TableField[] { Inventory.INVENTORY.INVENTORY_ADJUSTMENT_ORDER_ID }, Keys.INVENTORY_ADJUSTMENT_ORDER_PKEY, new TableField[] { InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.ID }, true);
    public static final ForeignKey<InventoryRecord, ManufacturingOrderRecord> INVENTORY__INVENTORY_MANUFACTURING_ORDER_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_manufacturing_order_id_fkey"), new TableField[] { Inventory.INVENTORY.MANUFACTURING_ORDER_ID }, Keys.MANUFACTURING_ORDER_PKEY, new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ID }, true);
    public static final ForeignKey<InventoryRecord, MaterialGoodRecord> INVENTORY__INVENTORY_MATERIAL_GOOD_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_material_good_id_fkey"), new TableField[] { Inventory.INVENTORY.MATERIAL_GOOD_ID }, Keys.MATERIAL_GOOD_PKEY, new TableField[] { MaterialGood.MATERIAL_GOOD.ID }, true);
    public static final ForeignKey<InventoryRecord, AccountRecord> INVENTORY__INVENTORY_OWNER_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_owner_id_fkey"), new TableField[] { Inventory.INVENTORY.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<InventoryRecord, ReceptionReceiptRecord> INVENTORY__INVENTORY_RECEPTION_RECEIPT_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_reception_receipt_id_fkey"), new TableField[] { Inventory.INVENTORY.RECEPTION_RECEIPT_ID }, Keys.RECEPTION_RECEIPT_PKEY, new TableField[] { ReceptionReceipt.RECEPTION_RECEIPT.ID }, true);
    public static final ForeignKey<InventoryRecord, SalesOrderRecord> INVENTORY__INVENTORY_SALES_ORDER_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_sales_order_id_fkey"), new TableField[] { Inventory.INVENTORY.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<InventoryRecord, ServicingOrderRecord> INVENTORY__INVENTORY_SERVICING_ORDER_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_servicing_order_id_fkey"), new TableField[] { Inventory.INVENTORY.SERVICING_ORDER_ID }, Keys.SERVICING_ORDER_PKEY, new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final ForeignKey<InventoryRecord, CompanyRecord> INVENTORY__INVENTORY_SUPPLIER_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_supplier_id_fkey"), new TableField[] { Inventory.INVENTORY.SUPPLIER_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<InventoryRecord, InventoryUnitRecord> INVENTORY__INVENTORY_TYPE_ID_FKEY = Internal.createForeignKey(Inventory.INVENTORY, DSL.name("inventory_type_id_fkey"), new TableField[] { Inventory.INVENTORY.UNIT_ID }, Keys.INVENTORY_TYPE_PKEY, new TableField[] { InventoryUnit.INVENTORY_UNIT.ID }, true);
    public static final ForeignKey<InventoryAdjustmentOrderRecord, AccountRecord> INVENTORY_ADJUSTMENT_ORDER__INVENTORY_ADJUSTMENT_ORDER_OWNER_ID_FKEY = Internal.createForeignKey(InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER, DSL.name("inventory_adjustment_order_owner_id_fkey"), new TableField[] { InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<InventoryCurrentStockRecord, MaterialGoodRecord> INVENTORY_CURRENT_STOCK__INVENTORY_CURRENT_STOCK_MATERIAL_GOOD_ID_FKEY = Internal.createForeignKey(InventoryCurrentStock.INVENTORY_CURRENT_STOCK, DSL.name("inventory_current_stock_material_good_id_fkey"), new TableField[] { InventoryCurrentStock.INVENTORY_CURRENT_STOCK.MATERIAL_GOOD_ID }, Keys.MATERIAL_GOOD_PKEY, new TableField[] { MaterialGood.MATERIAL_GOOD.ID }, true);
    public static final ForeignKey<InventoryCurrentStockRecord, InventoryUnitRecord> INVENTORY_CURRENT_STOCK__INVENTORY_CURRENT_STOCK_UNIT_ID_FKEY = Internal.createForeignKey(InventoryCurrentStock.INVENTORY_CURRENT_STOCK, DSL.name("inventory_current_stock_unit_id_fkey"), new TableField[] { InventoryCurrentStock.INVENTORY_CURRENT_STOCK.UNIT_ID }, Keys.INVENTORY_TYPE_PKEY, new TableField[] { InventoryUnit.INVENTORY_UNIT.ID }, true);
    public static final ForeignKey<InventoryUnitRecord, AccountRecord> INVENTORY_UNIT__INVENTORY_TYPE_OWNER_ID_FKEY = Internal.createForeignKey(InventoryUnit.INVENTORY_UNIT, DSL.name("inventory_type_owner_id_fkey"), new TableField[] { InventoryUnit.INVENTORY_UNIT.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<InvoiceRecord, CompanyRecord> INVOICE__INVOICE_CUSTOMER_ID_FKEY = Internal.createForeignKey(Invoice.INVOICE, DSL.name("invoice_customer_id_fkey"), new TableField[] { Invoice.INVOICE.CUSTOMER_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<InvoiceRecord, AccountRecord> INVOICE__INVOICE_OWNER_ID_FKEY = Internal.createForeignKey(Invoice.INVOICE, DSL.name("invoice_owner_id_fkey"), new TableField[] { Invoice.INVOICE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<InvoiceRecord, SalesOrderRecord> INVOICE__INVOICE_SALES_ORDER_ID_FKEY = Internal.createForeignKey(Invoice.INVOICE, DSL.name("invoice_sales_order_id_fkey"), new TableField[] { Invoice.INVOICE.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<LocationRecord, AccountRecord> LOCATION__LOCATION_OWNER_ID_FKEY = Internal.createForeignKey(Location.LOCATION, DSL.name("location_owner_id_fkey"), new TableField[] { Location.LOCATION.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ManufacturingOperationTemplateRecord, AccountRecord> MANUFACTURING_OPERATION_TEMPLATE__MANUFACTURING_OPERATION_TEMPLATE_OWNER_ID_FKEY = Internal.createForeignKey(ManufacturingOperationTemplate.MANUFACTURING_OPERATION_TEMPLATE, DSL.name("manufacturing_operation_template_owner_id_fkey"), new TableField[] { ManufacturingOperationTemplate.MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ManufacturingOrderRecord, UsersRecord> MANUFACTURING_ORDER__MANUFACTURING_ORDER_ASSIGNED_USER_ID_FKEY = Internal.createForeignKey(ManufacturingOrder.MANUFACTURING_ORDER, DSL.name("manufacturing_order_assigned_user_id_fkey"), new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ASSIGNED_USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<ManufacturingOrderRecord, AccountRecord> MANUFACTURING_ORDER__MANUFACTURING_ORDER_OWNER_ID_FKEY = Internal.createForeignKey(ManufacturingOrder.MANUFACTURING_ORDER, DSL.name("manufacturing_order_owner_id_fkey"), new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ManufacturingOrderRecord, ManufacturingOrderRecord> MANUFACTURING_ORDER__MANUFACTURING_ORDER_PARENT_ID_FKEY = Internal.createForeignKey(ManufacturingOrder.MANUFACTURING_ORDER, DSL.name("manufacturing_order_parent_id_fkey"), new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.PARENT_ID }, Keys.MANUFACTURING_ORDER_PKEY, new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ID }, true);
    public static final ForeignKey<ManufacturingOrderRecord, SalesOrderRecord> MANUFACTURING_ORDER__MANUFACTURING_ORDER_SALES_ORDER_ID_FKEY = Internal.createForeignKey(ManufacturingOrder.MANUFACTURING_ORDER, DSL.name("manufacturing_order_sales_order_id_fkey"), new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<ManufacturingOrderRecord, ServiceTemplateRecord> MANUFACTURING_ORDER__MANUFACTURING_ORDER_SERVICE_ID_FKEY = Internal.createForeignKey(ManufacturingOrder.MANUFACTURING_ORDER, DSL.name("manufacturing_order_service_id_fkey"), new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.SERVICE_ID }, Keys.SERVICE_TEMPLATE_PKEY, new TableField[] { ServiceTemplate.SERVICE_TEMPLATE.ID }, true);
    public static final ForeignKey<ManufacturingOrderNoteRecord, ManufacturingOrderRecord> MANUFACTURING_ORDER_NOTE__MANUFACTURING_ORDER_NOTE_MANUFACTURING_ORDER_ID_FKEY = Internal.createForeignKey(ManufacturingOrderNote.MANUFACTURING_ORDER_NOTE, DSL.name("manufacturing_order_note_manufacturing_order_id_fkey"), new TableField[] { ManufacturingOrderNote.MANUFACTURING_ORDER_NOTE.MANUFACTURING_ORDER_ID }, Keys.MANUFACTURING_ORDER_PKEY, new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ID }, true);
    public static final ForeignKey<ManufacturingOrderNoteRecord, NoteRecord> MANUFACTURING_ORDER_NOTE__MANUFACTURING_ORDER_NOTE_NOTE_ID_FKEY = Internal.createForeignKey(ManufacturingOrderNote.MANUFACTURING_ORDER_NOTE, DSL.name("manufacturing_order_note_note_id_fkey"), new TableField[] { ManufacturingOrderNote.MANUFACTURING_ORDER_NOTE.NOTE_ID }, Keys.NOTE_PKEY, new TableField[] { Note.NOTE.ID }, true);
    public static final ForeignKey<ManufacturingTaskRecord, ManufacturingOrderRecord> MANUFACTURING_TASK__MANUFACTURING_TASK_MANUFACTURING_ORDER_ID_FKEY = Internal.createForeignKey(ManufacturingTask.MANUFACTURING_TASK, DSL.name("manufacturing_task_manufacturing_order_id_fkey"), new TableField[] { ManufacturingTask.MANUFACTURING_TASK.MANUFACTURING_ORDER_ID }, Keys.MANUFACTURING_ORDER_PKEY, new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ID }, true);
    public static final ForeignKey<ManufacturingTaskRecord, AccountRecord> MANUFACTURING_TASK__MANUFACTURING_TASK_OWNER_ID_FKEY = Internal.createForeignKey(ManufacturingTask.MANUFACTURING_TASK, DSL.name("manufacturing_task_owner_id_fkey"), new TableField[] { ManufacturingTask.MANUFACTURING_TASK.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ManufacturingWorkstationRecord, AccountRecord> MANUFACTURING_WORKSTATION__MANUFACTURING_WORKSTATION_OWNER_ID_FKEY = Internal.createForeignKey(ManufacturingWorkstation.MANUFACTURING_WORKSTATION, DSL.name("manufacturing_workstation_owner_id_fkey"), new TableField[] { ManufacturingWorkstation.MANUFACTURING_WORKSTATION.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ManufacturingorderFileRecord, FileRecord> MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_FILE_ID_FKEY = Internal.createForeignKey(ManufacturingorderFile.MANUFACTURINGORDER_FILE, DSL.name("manufacturingorder_file_file_id_fkey"), new TableField[] { ManufacturingorderFile.MANUFACTURINGORDER_FILE.FILE_ID }, Keys.FILE_PKEY, new TableField[] { File.FILE.ID }, true);
    public static final ForeignKey<ManufacturingorderFileRecord, ManufacturingOrderRecord> MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_MANUFACTURING_ORDER_ID_FKEY = Internal.createForeignKey(ManufacturingorderFile.MANUFACTURINGORDER_FILE, DSL.name("manufacturingorder_file_manufacturing_order_id_fkey"), new TableField[] { ManufacturingorderFile.MANUFACTURINGORDER_FILE.MANUFACTURING_ORDER_ID }, Keys.MANUFACTURING_ORDER_PKEY, new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ID }, true);
    public static final ForeignKey<ManufacturingorderFileRecord, AccountRecord> MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_OWNER_ID_FKEY = Internal.createForeignKey(ManufacturingorderFile.MANUFACTURINGORDER_FILE, DSL.name("manufacturingorder_file_owner_id_fkey"), new TableField[] { ManufacturingorderFile.MANUFACTURINGORDER_FILE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ManufacturingtaskEmployeeRecord, ManufacturingTaskRecord> MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_MANUFACTURING_TASK_ID_FKEY = Internal.createForeignKey(ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE, DSL.name("manufacturingtask_employee_manufacturing_task_id_fkey"), new TableField[] { ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID }, Keys.MANUFACTURING_TASK_PKEY, new TableField[] { ManufacturingTask.MANUFACTURING_TASK.ID }, true);
    public static final ForeignKey<ManufacturingtaskEmployeeRecord, AccountRecord> MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_OWNER_ID_FKEY = Internal.createForeignKey(ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE, DSL.name("manufacturingtask_employee_owner_id_fkey"), new TableField[] { ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ManufacturingtaskEmployeeRecord, UsersRecord> MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_USER_ID_FKEY = Internal.createForeignKey(ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE, DSL.name("manufacturingtask_employee_user_id_fkey"), new TableField[] { ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<ManufacturingtaskWorkstationRecord, ManufacturingTaskRecord> MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_MANUFACTURING_TASK_ID_FKEY = Internal.createForeignKey(ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION, DSL.name("manufacturingtask_workstation_manufacturing_task_id_fkey"), new TableField[] { ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID }, Keys.MANUFACTURING_TASK_PKEY, new TableField[] { ManufacturingTask.MANUFACTURING_TASK.ID }, true);
    public static final ForeignKey<ManufacturingtaskWorkstationRecord, ManufacturingWorkstationRecord> MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_MANUFACTURING_WORKSTATION_ID_FKEY = Internal.createForeignKey(ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION, DSL.name("manufacturingtask_workstation_manufacturing_workstation_id_fkey"), new TableField[] { ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID }, Keys.MANUFACTURING_WORKSTATION_PKEY, new TableField[] { ManufacturingWorkstation.MANUFACTURING_WORKSTATION.ID }, true);
    public static final ForeignKey<ManufacturingtaskWorkstationRecord, AccountRecord> MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_OWNER_ID_FKEY = Internal.createForeignKey(ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION, DSL.name("manufacturingtask_workstation_owner_id_fkey"), new TableField[] { ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ManufacturingworkstationManufacturingoperationtemplateRecord, ManufacturingOperationTemplateRecord> MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANU_MANUFACTURING_OPERATION_TEMP_FKEY = Internal.createForeignKey(ManufacturingworkstationManufacturingoperationtemplate.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE, DSL.name("manufacturingworkstation_manu_manufacturing_operation_temp_fkey"), new TableField[] { ManufacturingworkstationManufacturingoperationtemplate.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID }, Keys.MANUFACTURING_OPERATION_TEMPLATE_PKEY, new TableField[] { ManufacturingOperationTemplate.MANUFACTURING_OPERATION_TEMPLATE.ID }, true);
    public static final ForeignKey<ManufacturingworkstationManufacturingoperationtemplateRecord, ManufacturingWorkstationRecord> MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANU_MANUFACTURING_WORKSTATION_ID_FKEY = Internal.createForeignKey(ManufacturingworkstationManufacturingoperationtemplate.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE, DSL.name("manufacturingworkstation_manu_manufacturing_workstation_id_fkey"), new TableField[] { ManufacturingworkstationManufacturingoperationtemplate.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_WORKSTATION_ID }, Keys.MANUFACTURING_WORKSTATION_PKEY, new TableField[] { ManufacturingWorkstation.MANUFACTURING_WORKSTATION.ID }, true);
    public static final ForeignKey<ManufacturingworkstationManufacturingoperationtemplateRecord, AccountRecord> MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTE_OWNER_ID_FKEY = Internal.createForeignKey(ManufacturingworkstationManufacturingoperationtemplate.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE, DSL.name("manufacturingworkstation_manufacturingoperationte_owner_id_fkey"), new TableField[] { ManufacturingworkstationManufacturingoperationtemplate.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<MaterialGoodRecord, CategoryRecord> MATERIAL_GOOD__MATERIAL_GOOD_CATEGORY_ID_FKEY = Internal.createForeignKey(MaterialGood.MATERIAL_GOOD, DSL.name("material_good_category_id_fkey"), new TableField[] { MaterialGood.MATERIAL_GOOD.CATEGORY_ID }, Keys.CATEGORY_PKEY, new TableField[] { Category.CATEGORY.ID }, true);
    public static final ForeignKey<MaterialGoodRecord, AccountRecord> MATERIAL_GOOD__MATERIAL_GOOD_OWNER_ID_FKEY = Internal.createForeignKey(MaterialGood.MATERIAL_GOOD, DSL.name("material_good_owner_id_fkey"), new TableField[] { MaterialGood.MATERIAL_GOOD.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<MaterialIssueNoteRecord, ManufacturingOrderRecord> MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_MANUFACTURING_ORDER_ID_FKEY = Internal.createForeignKey(MaterialIssueNote.MATERIAL_ISSUE_NOTE, DSL.name("material_issue_note_manufacturing_order_id_fkey"), new TableField[] { MaterialIssueNote.MATERIAL_ISSUE_NOTE.MANUFACTURING_ORDER_ID }, Keys.MANUFACTURING_ORDER_PKEY, new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ID }, true);
    public static final ForeignKey<MaterialIssueNoteRecord, AccountRecord> MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_OWNER_ID_FKEY = Internal.createForeignKey(MaterialIssueNote.MATERIAL_ISSUE_NOTE, DSL.name("material_issue_note_owner_id_fkey"), new TableField[] { MaterialIssueNote.MATERIAL_ISSUE_NOTE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<MaterialIssueNoteRecord, ServicingOrderRecord> MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_SERVICING_ORDER_ID_FKEY = Internal.createForeignKey(MaterialIssueNote.MATERIAL_ISSUE_NOTE, DSL.name("material_issue_note_servicing_order_id_fkey"), new TableField[] { MaterialIssueNote.MATERIAL_ISSUE_NOTE.SERVICING_ORDER_ID }, Keys.SERVICING_ORDER_PKEY, new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final ForeignKey<NoteRecord, UsersRecord> NOTE__NOTE_ADDED_BY_ID_FKEY = Internal.createForeignKey(Note.NOTE, DSL.name("note_added_by_id_fkey"), new TableField[] { Note.NOTE.ADDED_BY_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<NoteRecord, AccountRecord> NOTE__NOTE_OWNER_ID_FKEY = Internal.createForeignKey(Note.NOTE, DSL.name("note_owner_id_fkey"), new TableField[] { Note.NOTE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<NotificationRecord, AccountRecord> NOTIFICATION__NOTIFICATION_OWNER_ID_FKEY = Internal.createForeignKey(Notification.NOTIFICATION, DSL.name("notification_owner_id_fkey"), new TableField[] { Notification.NOTIFICATION.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<NotificationRecord, UsersRecord> NOTIFICATION__NOTIFICATION_RESOLVED_BY_FKEY = Internal.createForeignKey(Notification.NOTIFICATION, DSL.name("notification_resolved_by_fkey"), new TableField[] { Notification.NOTIFICATION.RESOLVED_BY }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<NotificationRecord, UsersRecord> NOTIFICATION__NOTIFICATION_TARGET_USER_ID_FKEY = Internal.createForeignKey(Notification.NOTIFICATION, DSL.name("notification_target_user_id_fkey"), new TableField[] { Notification.NOTIFICATION.TARGET_USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<NotificationReadByRecord, NotificationRecord> NOTIFICATION_READ_BY__NOTIFICATION_READ_BY_NOTIFICATION_ID_FKEY = Internal.createForeignKey(NotificationReadBy.NOTIFICATION_READ_BY, DSL.name("notification_read_by_notification_id_fkey"), new TableField[] { NotificationReadBy.NOTIFICATION_READ_BY.NOTIFICATION_ID }, Keys.NOTIFICATION_PKEY, new TableField[] { Notification.NOTIFICATION.ID }, true);
    public static final ForeignKey<NotificationReadByRecord, UsersRecord> NOTIFICATION_READ_BY__NOTIFICATION_READ_BY_USER_ID_FKEY = Internal.createForeignKey(NotificationReadBy.NOTIFICATION_READ_BY, DSL.name("notification_read_by_user_id_fkey"), new TableField[] { NotificationReadBy.NOTIFICATION_READ_BY.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<ProductFileRecord, FileRecord> PRODUCT_FILE__PRODUCT_FILE_FILE_ID_FKEY = Internal.createForeignKey(ProductFile.PRODUCT_FILE, DSL.name("product_file_file_id_fkey"), new TableField[] { ProductFile.PRODUCT_FILE.FILE_ID }, Keys.FILE_PKEY, new TableField[] { File.FILE.ID }, true);
    public static final ForeignKey<ProductFileRecord, AccountRecord> PRODUCT_FILE__PRODUCT_FILE_OWNER_ID_FKEY = Internal.createForeignKey(ProductFile.PRODUCT_FILE, DSL.name("product_file_owner_id_fkey"), new TableField[] { ProductFile.PRODUCT_FILE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ProductFileRecord, MaterialGoodRecord> PRODUCT_FILE__PRODUCT_FILE_PRODUCT_ID_FKEY = Internal.createForeignKey(ProductFile.PRODUCT_FILE, DSL.name("product_file_product_id_fkey"), new TableField[] { ProductFile.PRODUCT_FILE.PRODUCT_ID }, Keys.MATERIAL_GOOD_PKEY, new TableField[] { MaterialGood.MATERIAL_GOOD.ID }, true);
    public static final ForeignKey<PurchaseOrderRecord, UsersRecord> PURCHASE_ORDER__PURCHASE_ORDER_MANAGED_BY_FKEY = Internal.createForeignKey(PurchaseOrder.PURCHASE_ORDER, DSL.name("purchase_order_managed_by_fkey"), new TableField[] { PurchaseOrder.PURCHASE_ORDER.MANAGED_BY }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<PurchaseOrderRecord, AccountRecord> PURCHASE_ORDER__PURCHASE_ORDER_OWNER_ID_FKEY = Internal.createForeignKey(PurchaseOrder.PURCHASE_ORDER, DSL.name("purchase_order_owner_id_fkey"), new TableField[] { PurchaseOrder.PURCHASE_ORDER.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<PurchaseOrderRecord, CompanyRecord> PURCHASE_ORDER__PURCHASE_ORDER_SUPPLIER_ID_FKEY = Internal.createForeignKey(PurchaseOrder.PURCHASE_ORDER, DSL.name("purchase_order_supplier_id_fkey"), new TableField[] { PurchaseOrder.PURCHASE_ORDER.SUPPLIER_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<PurchaseOrderNoteRecord, NoteRecord> PURCHASE_ORDER_NOTE__PURCHASE_ORDER_NOTE_NOTE_ID_FKEY = Internal.createForeignKey(PurchaseOrderNote.PURCHASE_ORDER_NOTE, DSL.name("purchase_order_note_note_id_fkey"), new TableField[] { PurchaseOrderNote.PURCHASE_ORDER_NOTE.NOTE_ID }, Keys.NOTE_PKEY, new TableField[] { Note.NOTE.ID }, true);
    public static final ForeignKey<PurchaseOrderNoteRecord, PurchaseOrderRecord> PURCHASE_ORDER_NOTE__PURCHASE_ORDER_NOTE_PURCHASE_ORDER_ID_FKEY = Internal.createForeignKey(PurchaseOrderNote.PURCHASE_ORDER_NOTE, DSL.name("purchase_order_note_purchase_order_id_fkey"), new TableField[] { PurchaseOrderNote.PURCHASE_ORDER_NOTE.PURCHASE_ORDER_ID }, Keys.PURCHASE_ORDER_PKEY, new TableField[] { PurchaseOrder.PURCHASE_ORDER.ID }, true);
    public static final ForeignKey<PurchaseWishlistRecord, MaterialGoodRecord> PURCHASE_WISHLIST__PURCHASE_WISHLIST_MATERIAL_GOOD_ID_FKEY = Internal.createForeignKey(PurchaseWishlist.PURCHASE_WISHLIST, DSL.name("purchase_wishlist_material_good_id_fkey"), new TableField[] { PurchaseWishlist.PURCHASE_WISHLIST.MATERIAL_GOOD_ID }, Keys.MATERIAL_GOOD_PKEY, new TableField[] { MaterialGood.MATERIAL_GOOD.ID }, true);
    public static final ForeignKey<PurchaseWishlistRecord, AccountRecord> PURCHASE_WISHLIST__PURCHASE_WISHLIST_OWNER_ID_FKEY = Internal.createForeignKey(PurchaseWishlist.PURCHASE_WISHLIST, DSL.name("purchase_wishlist_owner_id_fkey"), new TableField[] { PurchaseWishlist.PURCHASE_WISHLIST.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<PurchaseWishlistRecord, CompanyRecord> PURCHASE_WISHLIST__PURCHASE_WISHLIST_SUPPLIER_ID_FKEY = Internal.createForeignKey(PurchaseWishlist.PURCHASE_WISHLIST, DSL.name("purchase_wishlist_supplier_id_fkey"), new TableField[] { PurchaseWishlist.PURCHASE_WISHLIST.SUPPLIER_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<ReceptionReceiptRecord, AccountRecord> RECEPTION_RECEIPT__RECEPTION_RECEIPT_OWNER_ID_FKEY = Internal.createForeignKey(ReceptionReceipt.RECEPTION_RECEIPT, DSL.name("reception_receipt_owner_id_fkey"), new TableField[] { ReceptionReceipt.RECEPTION_RECEIPT.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ReceptionReceiptRecord, PurchaseOrderRecord> RECEPTION_RECEIPT__RECEPTION_RECEIPT_PURCHASE_ORDER_ID_FKEY = Internal.createForeignKey(ReceptionReceipt.RECEPTION_RECEIPT, DSL.name("reception_receipt_purchase_order_id_fkey"), new TableField[] { ReceptionReceipt.RECEPTION_RECEIPT.PURCHASE_ORDER_ID }, Keys.PURCHASE_ORDER_PKEY, new TableField[] { PurchaseOrder.PURCHASE_ORDER.ID }, true);
    public static final ForeignKey<ReceptionReceiptRecord, CompanyRecord> RECEPTION_RECEIPT__RECEPTION_RECEIPT_SUPPLIER_ID_FKEY = Internal.createForeignKey(ReceptionReceipt.RECEPTION_RECEIPT, DSL.name("reception_receipt_supplier_id_fkey"), new TableField[] { ReceptionReceipt.RECEPTION_RECEIPT.SUPPLIER_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<ReceptionreceiptFileRecord, FileRecord> RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_FILE_ID_FKEY = Internal.createForeignKey(ReceptionreceiptFile.RECEPTIONRECEIPT_FILE, DSL.name("receptionreceipt_file_file_id_fkey"), new TableField[] { ReceptionreceiptFile.RECEPTIONRECEIPT_FILE.FILE_ID }, Keys.FILE_PKEY, new TableField[] { File.FILE.ID }, true);
    public static final ForeignKey<ReceptionreceiptFileRecord, AccountRecord> RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_OWNER_ID_FKEY = Internal.createForeignKey(ReceptionreceiptFile.RECEPTIONRECEIPT_FILE, DSL.name("receptionreceipt_file_owner_id_fkey"), new TableField[] { ReceptionreceiptFile.RECEPTIONRECEIPT_FILE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ReceptionreceiptFileRecord, ReceptionReceiptRecord> RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_RECEPTION_RECEIPT_ID_FKEY = Internal.createForeignKey(ReceptionreceiptFile.RECEPTIONRECEIPT_FILE, DSL.name("receptionreceipt_file_reception_receipt_id_fkey"), new TableField[] { ReceptionreceiptFile.RECEPTIONRECEIPT_FILE.RECEPTION_RECEIPT_ID }, Keys.RECEPTION_RECEIPT_PKEY, new TableField[] { ReceptionReceipt.RECEPTION_RECEIPT.ID }, true);
    public static final ForeignKey<ReservedInventoryRecord, InventoryUnitRecord> RESERVED_INVENTORY__RESERVED_INVENTORY_INVENTORY_TYPE_ID_FKEY = Internal.createForeignKey(ReservedInventory.RESERVED_INVENTORY, DSL.name("reserved_inventory_inventory_type_id_fkey"), new TableField[] { ReservedInventory.RESERVED_INVENTORY.INVENTORY_UNIT_ID }, Keys.INVENTORY_TYPE_PKEY, new TableField[] { InventoryUnit.INVENTORY_UNIT.ID }, true);
    public static final ForeignKey<ReservedInventoryRecord, LocationRecord> RESERVED_INVENTORY__RESERVED_INVENTORY_LOCATION_ID_FKEY = Internal.createForeignKey(ReservedInventory.RESERVED_INVENTORY, DSL.name("reserved_inventory_location_id_fkey"), new TableField[] { ReservedInventory.RESERVED_INVENTORY.LOCATION_ID }, Keys.LOCATION_PKEY, new TableField[] { Location.LOCATION.ID }, true);
    public static final ForeignKey<ReservedInventoryRecord, ManufacturingOrderRecord> RESERVED_INVENTORY__RESERVED_INVENTORY_MANUFACTURING_ORDER_ID_FKEY = Internal.createForeignKey(ReservedInventory.RESERVED_INVENTORY, DSL.name("reserved_inventory_manufacturing_order_id_fkey"), new TableField[] { ReservedInventory.RESERVED_INVENTORY.MANUFACTURING_ORDER_ID }, Keys.MANUFACTURING_ORDER_PKEY, new TableField[] { ManufacturingOrder.MANUFACTURING_ORDER.ID }, true);
    public static final ForeignKey<ReservedInventoryRecord, MaterialGoodRecord> RESERVED_INVENTORY__RESERVED_INVENTORY_MATERIAL_GOOD_ID_FKEY = Internal.createForeignKey(ReservedInventory.RESERVED_INVENTORY, DSL.name("reserved_inventory_material_good_id_fkey"), new TableField[] { ReservedInventory.RESERVED_INVENTORY.MATERIAL_GOOD_ID }, Keys.MATERIAL_GOOD_PKEY, new TableField[] { MaterialGood.MATERIAL_GOOD.ID }, true);
    public static final ForeignKey<ReservedInventoryRecord, AccountRecord> RESERVED_INVENTORY__RESERVED_INVENTORY_OWNER_ID_FKEY = Internal.createForeignKey(ReservedInventory.RESERVED_INVENTORY, DSL.name("reserved_inventory_owner_id_fkey"), new TableField[] { ReservedInventory.RESERVED_INVENTORY.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ReservedInventoryRecord, SalesOrderRecord> RESERVED_INVENTORY__RESERVED_INVENTORY_SALES_ORDER_ID_FKEY = Internal.createForeignKey(ReservedInventory.RESERVED_INVENTORY, DSL.name("reserved_inventory_sales_order_id_fkey"), new TableField[] { ReservedInventory.RESERVED_INVENTORY.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<ReservedInventoryRecord, ServicingOrderRecord> RESERVED_INVENTORY__RESERVED_INVENTORY_SERVICING_ORDER_ID_FKEY = Internal.createForeignKey(ReservedInventory.RESERVED_INVENTORY, DSL.name("reserved_inventory_servicing_order_id_fkey"), new TableField[] { ReservedInventory.RESERVED_INVENTORY.SERVICING_ORDER_ID }, Keys.SERVICING_ORDER_PKEY, new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final ForeignKey<SalesOrderRecord, CompanyRecord> SALES_ORDER__SALES_ORDER_CUSTOMER_ID_FKEY = Internal.createForeignKey(SalesOrder.SALES_ORDER, DSL.name("sales_order_customer_id_fkey"), new TableField[] { SalesOrder.SALES_ORDER.CUSTOMER_ID }, Keys.COMPANY_PKEY, new TableField[] { Company.COMPANY.ID }, true);
    public static final ForeignKey<SalesOrderRecord, AccountRecord> SALES_ORDER__SALES_ORDER_OWNER_ID_FKEY = Internal.createForeignKey(SalesOrder.SALES_ORDER, DSL.name("sales_order_owner_id_fkey"), new TableField[] { SalesOrder.SALES_ORDER.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<SalesOrderNoteRecord, NoteRecord> SALES_ORDER_NOTE__SALES_ORDER_NOTE_NOTE_ID_FKEY = Internal.createForeignKey(SalesOrderNote.SALES_ORDER_NOTE, DSL.name("sales_order_note_note_id_fkey"), new TableField[] { SalesOrderNote.SALES_ORDER_NOTE.NOTE_ID }, Keys.NOTE_PKEY, new TableField[] { Note.NOTE.ID }, true);
    public static final ForeignKey<SalesOrderNoteRecord, SalesOrderRecord> SALES_ORDER_NOTE__SALES_ORDER_NOTE_SALES_ORDER_ID_FKEY = Internal.createForeignKey(SalesOrderNote.SALES_ORDER_NOTE, DSL.name("sales_order_note_sales_order_id_fkey"), new TableField[] { SalesOrderNote.SALES_ORDER_NOTE.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<SalesorderFileRecord, FileRecord> SALESORDER_FILE__SALESORDER_FILE_FILE_ID_FKEY = Internal.createForeignKey(SalesorderFile.SALESORDER_FILE, DSL.name("salesorder_file_file_id_fkey"), new TableField[] { SalesorderFile.SALESORDER_FILE.FILE_ID }, Keys.FILE_PKEY, new TableField[] { File.FILE.ID }, true);
    public static final ForeignKey<SalesorderFileRecord, AccountRecord> SALESORDER_FILE__SALESORDER_FILE_OWNER_ID_FKEY = Internal.createForeignKey(SalesorderFile.SALESORDER_FILE, DSL.name("salesorder_file_owner_id_fkey"), new TableField[] { SalesorderFile.SALESORDER_FILE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<SalesorderFileRecord, SalesOrderRecord> SALESORDER_FILE__SALESORDER_FILE_SALES_ORDER_ID_FKEY = Internal.createForeignKey(SalesorderFile.SALESORDER_FILE, DSL.name("salesorder_file_sales_order_id_fkey"), new TableField[] { SalesorderFile.SALESORDER_FILE.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<SalesorderGoodsaccompanyingnoteRecord, GoodsAccompanyingNoteRecord> SALESORDER_GOODSACCOMPANYINGNOTE__SALESORDER_GOODSACCOMPANYINGNOT_GOODS_ACCOMPANYING_NOTE_ID_FKEY = Internal.createForeignKey(SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE, DSL.name("salesorder_goodsaccompanyingnot_goods_accompanying_note_id_fkey"), new TableField[] { SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID }, Keys.GOODS_ACCOMPANYING_NOTE_PKEY, new TableField[] { GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.ID }, true);
    public static final ForeignKey<SalesorderGoodsaccompanyingnoteRecord, SalesOrderRecord> SALESORDER_GOODSACCOMPANYINGNOTE__SALESORDER_GOODSACCOMPANYINGNOTE_SALES_ORDER_ID_FKEY = Internal.createForeignKey(SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE, DSL.name("salesorder_goodsaccompanyingnote_sales_order_id_fkey"), new TableField[] { SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<SequenceRecord, AccountRecord> SEQUENCE__SEQUENCE_OWNER_ID_FKEY = Internal.createForeignKey(Sequence.SEQUENCE, DSL.name("sequence_owner_id_fkey"), new TableField[] { Sequence.SEQUENCE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ServiceTemplateRecord, AccountRecord> SERVICE_TEMPLATE__SERVICE_TEMPLATE_OWNER_ID_FKEY = Internal.createForeignKey(ServiceTemplate.SERVICE_TEMPLATE, DSL.name("service_template_owner_id_fkey"), new TableField[] { ServiceTemplate.SERVICE_TEMPLATE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ServicingOrderRecord, UsersRecord> SERVICING_ORDER__SERVICING_ORDER_ASSIGNED_TO_FKEY = Internal.createForeignKey(ServicingOrder.SERVICING_ORDER, DSL.name("servicing_order_assigned_to_fkey"), new TableField[] { ServicingOrder.SERVICING_ORDER.ASSIGNED_TO }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<ServicingOrderRecord, AccountRecord> SERVICING_ORDER__SERVICING_ORDER_OWNER_ID_FKEY = Internal.createForeignKey(ServicingOrder.SERVICING_ORDER, DSL.name("servicing_order_owner_id_fkey"), new TableField[] { ServicingOrder.SERVICING_ORDER.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ServicingOrderRecord, SalesOrderRecord> SERVICING_ORDER__SERVICING_ORDER_SALES_ORDER_ID_FKEY = Internal.createForeignKey(ServicingOrder.SERVICING_ORDER, DSL.name("servicing_order_sales_order_id_fkey"), new TableField[] { ServicingOrder.SERVICING_ORDER.SALES_ORDER_ID }, Keys.SALES_ORDER_PKEY, new TableField[] { SalesOrder.SALES_ORDER.ID }, true);
    public static final ForeignKey<ServicingOrderRecord, ServiceTemplateRecord> SERVICING_ORDER__SERVICING_ORDER_SERVICE_ID_FKEY = Internal.createForeignKey(ServicingOrder.SERVICING_ORDER, DSL.name("servicing_order_service_id_fkey"), new TableField[] { ServicingOrder.SERVICING_ORDER.SERVICE_ID }, Keys.SERVICE_TEMPLATE_PKEY, new TableField[] { ServiceTemplate.SERVICE_TEMPLATE.ID }, true);
    public static final ForeignKey<ServicingOrderNoteRecord, NoteRecord> SERVICING_ORDER_NOTE__SERVICING_ORDER_NOTE_NOTE_ID_FKEY = Internal.createForeignKey(ServicingOrderNote.SERVICING_ORDER_NOTE, DSL.name("servicing_order_note_note_id_fkey"), new TableField[] { ServicingOrderNote.SERVICING_ORDER_NOTE.NOTE_ID }, Keys.NOTE_PKEY, new TableField[] { Note.NOTE.ID }, true);
    public static final ForeignKey<ServicingOrderNoteRecord, ServicingOrderRecord> SERVICING_ORDER_NOTE__SERVICING_ORDER_NOTE_SERVICING_ORDER_ID_FKEY = Internal.createForeignKey(ServicingOrderNote.SERVICING_ORDER_NOTE, DSL.name("servicing_order_note_servicing_order_id_fkey"), new TableField[] { ServicingOrderNote.SERVICING_ORDER_NOTE.SERVICING_ORDER_ID }, Keys.SERVICING_ORDER_PKEY, new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final ForeignKey<ServicingorderFileRecord, FileRecord> SERVICINGORDER_FILE__SERVICINGORDER_FILE_FILE_ID_FKEY = Internal.createForeignKey(ServicingorderFile.SERVICINGORDER_FILE, DSL.name("servicingorder_file_file_id_fkey"), new TableField[] { ServicingorderFile.SERVICINGORDER_FILE.FILE_ID }, Keys.FILE_PKEY, new TableField[] { File.FILE.ID }, true);
    public static final ForeignKey<ServicingorderFileRecord, AccountRecord> SERVICINGORDER_FILE__SERVICINGORDER_FILE_OWNER_ID_FKEY = Internal.createForeignKey(ServicingorderFile.SERVICINGORDER_FILE, DSL.name("servicingorder_file_owner_id_fkey"), new TableField[] { ServicingorderFile.SERVICINGORDER_FILE.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<ServicingorderFileRecord, ServicingOrderRecord> SERVICINGORDER_FILE__SERVICINGORDER_FILE_SERVICING_ORDER_ID_FKEY = Internal.createForeignKey(ServicingorderFile.SERVICINGORDER_FILE, DSL.name("servicingorder_file_servicing_order_id_fkey"), new TableField[] { ServicingorderFile.SERVICINGORDER_FILE.SERVICING_ORDER_ID }, Keys.SERVICING_ORDER_PKEY, new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final ForeignKey<ServicingorderGoodsaccompanyingnoteRecord, GoodsAccompanyingNoteRecord> SERVICINGORDER_GOODSACCOMPANYINGNOTE__SERVICINGORDER_GOODSACCOMPANYIN_GOODS_ACCOMPANYING_NOTE_ID_FKEY = Internal.createForeignKey(ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE, DSL.name("servicingorder_goodsaccompanyin_goods_accompanying_note_id_fkey"), new TableField[] { ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID }, Keys.GOODS_ACCOMPANYING_NOTE_PKEY, new TableField[] { GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.ID }, true);
    public static final ForeignKey<ServicingorderGoodsaccompanyingnoteRecord, ServicingOrderRecord> SERVICINGORDER_GOODSACCOMPANYINGNOTE__SERVICINGORDER_GOODSACCOMPANYINGNOTE_SERVICING_ORDER_ID_FKEY = Internal.createForeignKey(ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE, DSL.name("servicingorder_goodsaccompanyingnote_servicing_order_id_fkey"), new TableField[] { ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE.SERVICING_ORDER_ID }, Keys.SERVICING_ORDER_PKEY, new TableField[] { ServicingOrder.SERVICING_ORDER.ID }, true);
    public static final ForeignKey<SystemEventRecord, AccountRecord> SYSTEM_EVENT__SYSTEM_EVENT_OWNER_ID_FKEY = Internal.createForeignKey(SystemEvent.SYSTEM_EVENT, DSL.name("system_event_owner_id_fkey"), new TableField[] { SystemEvent.SYSTEM_EVENT.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
    public static final ForeignKey<SystemEventRecord, UsersRecord> SYSTEM_EVENT__SYSTEM_EVENT_USER_ID_FKEY = Internal.createForeignKey(SystemEvent.SYSTEM_EVENT, DSL.name("system_event_user_id_fkey"), new TableField[] { SystemEvent.SYSTEM_EVENT.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<UsersRecord, AccountRecord> USERS__USERS_OWNER_ID_FKEY = Internal.createForeignKey(Users.USERS, DSL.name("users_owner_id_fkey"), new TableField[] { Users.USERS.OWNER_ID }, Keys.ACCOUNT_PKEY, new TableField[] { Account.ACCOUNT.ID }, true);
}
