/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ManufacturingOrder;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingOrderRecord extends UpdatableRecordImpl<ManufacturingOrderRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.manufacturing_order.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.manufacturing_order.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.manufacturing_order.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.manufacturing_order.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.manufacturing_order.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.manufacturing_order.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.manufacturing_order.number</code>.
     */
    public void setNumber(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.number</code>.
     */
    public String getNumber() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.manufacturing_order.production_deadline</code>.
     */
    public void setProductionDeadline(LocalDateTime value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.production_deadline</code>.
     */
    public LocalDateTime getProductionDeadline() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>public.manufacturing_order.status</code>.
     */
    public void setStatus(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.status</code>.
     */
    public String getStatus() {
        return (String) get(8);
    }

    /**
     * Setter for <code>public.manufacturing_order.product_id</code>.
     */
    public void setProductId(UUID value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.product_id</code>.
     */
    public UUID getProductId() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>public.manufacturing_order.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(10);
    }

    /**
     * Setter for <code>public.manufacturing_order.manufactured_products</code>.
     */
    public void setManufacturedProducts(JSONB value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.manufactured_products</code>.
     */
    public JSONB getManufacturedProducts() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>public.manufacturing_order.ranking</code>.
     */
    public void setRanking(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.ranking</code>.
     */
    public Integer getRanking() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>public.manufacturing_order.notes</code>.
     */
    public void setNotes(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.notes</code>.
     */
    public String getNotes() {
        return (String) get(13);
    }

    /**
     * Setter for <code>public.manufacturing_order.required_materials</code>.
     */
    public void setRequiredMaterials(JSONB value) {
        set(14, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.required_materials</code>.
     */
    public JSONB getRequiredMaterials() {
        return (JSONB) get(14);
    }

    /**
     * Setter for
     * <code>public.manufacturing_order.manufacturing_operations</code>.
     */
    public void setManufacturingOperations(JSONB value) {
        set(15, value);
    }

    /**
     * Getter for
     * <code>public.manufacturing_order.manufacturing_operations</code>.
     */
    public JSONB getManufacturingOperations() {
        return (JSONB) get(15);
    }

    /**
     * Setter for <code>public.manufacturing_order.custom_product</code>.
     */
    public void setCustomProduct(Boolean value) {
        set(16, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.custom_product</code>.
     */
    public Boolean getCustomProduct() {
        return (Boolean) get(16);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(17, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(17);
    }

    /**
     * Setter for <code>public.manufacturing_order.service_id</code>.
     */
    public void setServiceId(UUID value) {
        set(18, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.service_id</code>.
     */
    public UUID getServiceId() {
        return (UUID) get(18);
    }

    /**
     * Setter for <code>public.manufacturing_order.manufacturing_costs</code>.
     */
    public void setManufacturingCosts(JSONB value) {
        set(19, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.manufacturing_costs</code>.
     */
    public JSONB getManufacturingCosts() {
        return (JSONB) get(19);
    }

    /**
     * Setter for <code>public.manufacturing_order.assigned_user_id</code>.
     */
    public void setAssignedUserId(UUID value) {
        set(20, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.assigned_user_id</code>.
     */
    public UUID getAssignedUserId() {
        return (UUID) get(20);
    }

    /**
     * Setter for
     * <code>public.manufacturing_order.base64_client_signature</code>.
     */
    public void setBase64ClientSignature(String value) {
        set(21, value);
    }

    /**
     * Getter for
     * <code>public.manufacturing_order.base64_client_signature</code>.
     */
    public String getBase64ClientSignature() {
        return (String) get(21);
    }

    /**
     * Setter for <code>public.manufacturing_order.parent_id</code>.
     */
    public void setParentId(UUID value) {
        set(22, value);
    }

    /**
     * Getter for <code>public.manufacturing_order.parent_id</code>.
     */
    public UUID getParentId() {
        return (UUID) get(22);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ManufacturingOrderRecord
     */
    public ManufacturingOrderRecord() {
        super(ManufacturingOrder.MANUFACTURING_ORDER);
    }

    /**
     * Create a detached, initialised ManufacturingOrderRecord
     */
    public ManufacturingOrderRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID salesOrderId, String number, LocalDateTime productionDeadline, String status, UUID productId, BigDecimal quantity, JSONB manufacturedProducts, Integer ranking, String notes, JSONB requiredMaterials, JSONB manufacturingOperations, Boolean customProduct, Object textSearch, UUID serviceId, JSONB manufacturingCosts, UUID assignedUserId, String base64ClientSignature, UUID parentId) {
        super(ManufacturingOrder.MANUFACTURING_ORDER);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setSalesOrderId(salesOrderId);
        setNumber(number);
        setProductionDeadline(productionDeadline);
        setStatus(status);
        setProductId(productId);
        setQuantity(quantity);
        setManufacturedProducts(manufacturedProducts);
        setRanking(ranking);
        setNotes(notes);
        setRequiredMaterials(requiredMaterials);
        setManufacturingOperations(manufacturingOperations);
        setCustomProduct(customProduct);
        setTextSearch(textSearch);
        setServiceId(serviceId);
        setManufacturingCosts(manufacturingCosts);
        setAssignedUserId(assignedUserId);
        setBase64ClientSignature(base64ClientSignature);
        setParentId(parentId);
    }
}
