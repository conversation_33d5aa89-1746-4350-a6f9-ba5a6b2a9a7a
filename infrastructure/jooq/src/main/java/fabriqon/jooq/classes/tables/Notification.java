/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.NotificationRecord;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Notification extends TableImpl<NotificationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.notification</code>
     */
    public static final Notification NOTIFICATION = new Notification();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<NotificationRecord> getRecordType() {
        return NotificationRecord.class;
    }

    /**
     * The column <code>public.notification.id</code>.
     */
    public final TableField<NotificationRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.notification.create_time</code>.
     */
    public final TableField<NotificationRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.notification.update_time</code>.
     */
    public final TableField<NotificationRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.notification.deleted</code>.
     */
    public final TableField<NotificationRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.notification.owner_id</code>.
     */
    public final TableField<NotificationRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.notification.type</code>.
     */
    public final TableField<NotificationRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.notification.section</code>.
     */
    public final TableField<NotificationRecord, String> SECTION = createField(DSL.name("section"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.notification.target_entity_id</code>.
     */
    public final TableField<NotificationRecord, UUID> TARGET_ENTITY_ID = createField(DSL.name("target_entity_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.notification.resolved_at</code>.
     */
    public final TableField<NotificationRecord, LocalDateTime> RESOLVED_AT = createField(DSL.name("resolved_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.notification.resolved_by</code>.
     */
    public final TableField<NotificationRecord, UUID> RESOLVED_BY = createField(DSL.name("resolved_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.notification.triggered_by</code>.
     */
    public final TableField<NotificationRecord, String> TRIGGERED_BY = createField(DSL.name("triggered_by"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.notification.details</code>.
     */
    public final TableField<NotificationRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.notification.target_user_id</code>.
     */
    public final TableField<NotificationRecord, UUID> TARGET_USER_ID = createField(DSL.name("target_user_id"), SQLDataType.UUID, this, "");

    private Notification(Name alias, Table<NotificationRecord> aliased) {
        this(alias, aliased, null);
    }

    private Notification(Name alias, Table<NotificationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.notification</code> table reference
     */
    public Notification(String alias) {
        this(DSL.name(alias), NOTIFICATION);
    }

    /**
     * Create an aliased <code>public.notification</code> table reference
     */
    public Notification(Name alias) {
        this(alias, NOTIFICATION);
    }

    /**
     * Create a <code>public.notification</code> table reference
     */
    public Notification() {
        this(DSL.name("notification"), null);
    }

    public <O extends Record> Notification(Table<O> child, ForeignKey<O, NotificationRecord> key) {
        super(child, key, NOTIFICATION);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<NotificationRecord> getPrimaryKey() {
        return Keys.NOTIFICATION_PKEY;
    }

    @Override
    public List<ForeignKey<NotificationRecord, ?>> getReferences() {
        return Arrays.asList(Keys.NOTIFICATION__NOTIFICATION_OWNER_ID_FKEY, Keys.NOTIFICATION__NOTIFICATION_RESOLVED_BY_FKEY, Keys.NOTIFICATION__NOTIFICATION_TARGET_USER_ID_FKEY);
    }

    private transient Account _account;
    private transient Users _notificationResolvedByFkey;
    private transient Users _notificationTargetUserIdFkey;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.NOTIFICATION__NOTIFICATION_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table, via
     * the <code>notification_resolved_by_fkey</code> key.
     */
    public Users notificationResolvedByFkey() {
        if (_notificationResolvedByFkey == null)
            _notificationResolvedByFkey = new Users(this, Keys.NOTIFICATION__NOTIFICATION_RESOLVED_BY_FKEY);

        return _notificationResolvedByFkey;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table, via
     * the <code>notification_target_user_id_fkey</code> key.
     */
    public Users notificationTargetUserIdFkey() {
        if (_notificationTargetUserIdFkey == null)
            _notificationTargetUserIdFkey = new Users(this, Keys.NOTIFICATION__NOTIFICATION_TARGET_USER_ID_FKEY);

        return _notificationTargetUserIdFkey;
    }

    @Override
    public Notification as(String alias) {
        return new Notification(DSL.name(alias), this);
    }

    @Override
    public Notification as(Name alias) {
        return new Notification(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Notification rename(String name) {
        return new Notification(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Notification rename(Name name) {
        return new Notification(name, null);
    }

    // -------------------------------------------------------------------------
    // Row13 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row13<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, UUID, LocalDateTime, UUID, String, JSONB, UUID> fieldsRow() {
        return (Row13) super.fieldsRow();
    }
}
