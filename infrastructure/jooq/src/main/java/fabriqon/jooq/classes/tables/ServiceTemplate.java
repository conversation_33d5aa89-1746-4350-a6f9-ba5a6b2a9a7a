/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ServiceTemplateRecord;
import fabriqon.jooq.converters.CurrencyConverter;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Currency;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServiceTemplate extends TableImpl<ServiceTemplateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.service_template</code>
     */
    public static final ServiceTemplate SERVICE_TEMPLATE = new ServiceTemplate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ServiceTemplateRecord> getRecordType() {
        return ServiceTemplateRecord.class;
    }

    /**
     * The column <code>public.service_template.id</code>.
     */
    public final TableField<ServiceTemplateRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.service_template.create_time</code>.
     */
    public final TableField<ServiceTemplateRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.service_template.update_time</code>.
     */
    public final TableField<ServiceTemplateRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.service_template.deleted</code>.
     */
    public final TableField<ServiceTemplateRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.service_template.owner_id</code>.
     */
    public final TableField<ServiceTemplateRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.service_template.name</code>.
     */
    public final TableField<ServiceTemplateRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.service_template.vat_rate</code>.
     */
    public final TableField<ServiceTemplateRecord, BigDecimal> VAT_RATE = createField(DSL.name("vat_rate"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.service_template.measurement_unit</code>.
     */
    public final TableField<ServiceTemplateRecord, String> MEASUREMENT_UNIT = createField(DSL.name("measurement_unit"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.service_template.sell_price_amount</code>.
     */
    public final TableField<ServiceTemplateRecord, Integer> SELL_PRICE_AMOUNT = createField(DSL.name("sell_price_amount"), SQLDataType.INTEGER, this, "");

    /**
     * The column <code>public.service_template.sell_price_currency</code>.
     */
    public final TableField<ServiceTemplateRecord, Currency> SELL_PRICE_CURRENCY = createField(DSL.name("sell_price_currency"), SQLDataType.VARCHAR(3), this, "", new CurrencyConverter());

    /**
     * The column <code>public.service_template.cost_amount</code>.
     */
    public final TableField<ServiceTemplateRecord, Integer> COST_AMOUNT = createField(DSL.name("cost_amount"), SQLDataType.INTEGER, this, "");

    /**
     * The column <code>public.service_template.cost_currency</code>.
     */
    public final TableField<ServiceTemplateRecord, Currency> COST_CURRENCY = createField(DSL.name("cost_currency"), SQLDataType.VARCHAR(3), this, "", new CurrencyConverter());

    private ServiceTemplate(Name alias, Table<ServiceTemplateRecord> aliased) {
        this(alias, aliased, null);
    }

    private ServiceTemplate(Name alias, Table<ServiceTemplateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.service_template</code> table reference
     */
    public ServiceTemplate(String alias) {
        this(DSL.name(alias), SERVICE_TEMPLATE);
    }

    /**
     * Create an aliased <code>public.service_template</code> table reference
     */
    public ServiceTemplate(Name alias) {
        this(alias, SERVICE_TEMPLATE);
    }

    /**
     * Create a <code>public.service_template</code> table reference
     */
    public ServiceTemplate() {
        this(DSL.name("service_template"), null);
    }

    public <O extends Record> ServiceTemplate(Table<O> child, ForeignKey<O, ServiceTemplateRecord> key) {
        super(child, key, SERVICE_TEMPLATE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<ServiceTemplateRecord> getPrimaryKey() {
        return Keys.SERVICE_TEMPLATE_PKEY;
    }

    @Override
    public List<ForeignKey<ServiceTemplateRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SERVICE_TEMPLATE__SERVICE_TEMPLATE_OWNER_ID_FKEY);
    }

    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.SERVICE_TEMPLATE__SERVICE_TEMPLATE_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public ServiceTemplate as(String alias) {
        return new ServiceTemplate(DSL.name(alias), this);
    }

    @Override
    public ServiceTemplate as(Name alias) {
        return new ServiceTemplate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ServiceTemplate rename(String name) {
        return new ServiceTemplate(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ServiceTemplate rename(Name name) {
        return new ServiceTemplate(name, null);
    }

    // -------------------------------------------------------------------------
    // Row12 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row12<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, BigDecimal, String, Integer, Currency, Integer, Currency> fieldsRow() {
        return (Row12) super.fieldsRow();
    }
}
