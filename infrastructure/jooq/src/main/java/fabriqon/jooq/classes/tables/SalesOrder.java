/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Indexes;
import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.SalesOrderRecord;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesOrder extends TableImpl<SalesOrderRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.sales_order</code>
     */
    public static final SalesOrder SALES_ORDER = new SalesOrder();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SalesOrderRecord> getRecordType() {
        return SalesOrderRecord.class;
    }

    /**
     * The column <code>public.sales_order.id</code>.
     */
    public final TableField<SalesOrderRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.sales_order.create_time</code>.
     */
    public final TableField<SalesOrderRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.sales_order.update_time</code>.
     */
    public final TableField<SalesOrderRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.sales_order.deleted</code>.
     */
    public final TableField<SalesOrderRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.sales_order.owner_id</code>.
     */
    public final TableField<SalesOrderRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.sales_order.customer_id</code>.
     */
    public final TableField<SalesOrderRecord, UUID> CUSTOMER_ID = createField(DSL.name("customer_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.sales_order.number</code>.
     */
    public final TableField<SalesOrderRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.sales_order.ranking</code>.
     */
    public final TableField<SalesOrderRecord, Integer> RANKING = createField(DSL.name("ranking"), SQLDataType.INTEGER, this, "");

    /**
     * The column <code>public.sales_order.delivery_deadline</code>.
     */
    public final TableField<SalesOrderRecord, LocalDateTime> DELIVERY_DEADLINE = createField(DSL.name("delivery_deadline"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.sales_order.status</code>.
     */
    public final TableField<SalesOrderRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(32).nullable(false), this, "");

    /**
     * The column <code>public.sales_order.items</code>.
     */
    public final TableField<SalesOrderRecord, JSONB> ITEMS = createField(DSL.name("items"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.sales_order.shipping_address</code>.
     */
    public final TableField<SalesOrderRecord, JSONB> SHIPPING_ADDRESS = createField(DSL.name("shipping_address"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.sales_order.notes</code>.
     */
    public final TableField<SalesOrderRecord, String> NOTES = createField(DSL.name("notes"), SQLDataType.VARCHAR(10000), this, "");

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public final TableField<SalesOrderRecord, Object> TEXT_SEARCH = createField(DSL.name("text_search"), org.jooq.impl.DefaultDataType.getDefaultDataType("\"pg_catalog\".\"tsvector\""), this, "");

    /**
     * The column <code>public.sales_order.offer_date</code>.
     */
    public final TableField<SalesOrderRecord, LocalDateTime> OFFER_DATE = createField(DSL.name("offer_date"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.sales_order.offer_expiration</code>.
     */
    public final TableField<SalesOrderRecord, LocalDateTime> OFFER_EXPIRATION = createField(DSL.name("offer_expiration"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.sales_order.rendering_details</code>.
     */
    public final TableField<SalesOrderRecord, JSONB> RENDERING_DETAILS = createField(DSL.name("rendering_details"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.sales_order.customer_notes</code>.
     */
    public final TableField<SalesOrderRecord, String> CUSTOMER_NOTES = createField(DSL.name("customer_notes"), SQLDataType.CLOB, this, "");

    private SalesOrder(Name alias, Table<SalesOrderRecord> aliased) {
        this(alias, aliased, null);
    }

    private SalesOrder(Name alias, Table<SalesOrderRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.sales_order</code> table reference
     */
    public SalesOrder(String alias) {
        this(DSL.name(alias), SALES_ORDER);
    }

    /**
     * Create an aliased <code>public.sales_order</code> table reference
     */
    public SalesOrder(Name alias) {
        this(alias, SALES_ORDER);
    }

    /**
     * Create a <code>public.sales_order</code> table reference
     */
    public SalesOrder() {
        this(DSL.name("sales_order"), null);
    }

    public <O extends Record> SalesOrder(Table<O> child, ForeignKey<O, SalesOrderRecord> key) {
        super(child, key, SALES_ORDER);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.SALES_ORDER_TEXT_SEARCH_IDX);
    }

    @Override
    public UniqueKey<SalesOrderRecord> getPrimaryKey() {
        return Keys.SALES_ORDER_PKEY;
    }

    @Override
    public List<ForeignKey<SalesOrderRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SALES_ORDER__SALES_ORDER_OWNER_ID_FKEY, Keys.SALES_ORDER__SALES_ORDER_CUSTOMER_ID_FKEY);
    }

    private transient Account _account;
    private transient Company _company;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.SALES_ORDER__SALES_ORDER_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.SALES_ORDER__SALES_ORDER_CUSTOMER_ID_FKEY);

        return _company;
    }

    @Override
    public SalesOrder as(String alias) {
        return new SalesOrder(DSL.name(alias), this);
    }

    @Override
    public SalesOrder as(Name alias) {
        return new SalesOrder(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesOrder rename(String name) {
        return new SalesOrder(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesOrder rename(Name name) {
        return new SalesOrder(name, null);
    }

    // -------------------------------------------------------------------------
    // Row18 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row18<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, Integer, LocalDateTime, String, JSONB, JSONB, String, Object, LocalDateTime, LocalDateTime, JSONB, String> fieldsRow() {
        return (Row18) super.fieldsRow();
    }
}
