/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.GoodsAccompanyingNote;
import org.jooq.*;
import org.jooq.impl.UpdatableRecordImpl;

import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GoodsAccompanyingNoteRecord extends UpdatableRecordImpl<GoodsAccompanyingNoteRecord> implements Record13<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, LocalDateTime, JSONB, JSONB, String, String, JSONB, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.goods_accompanying_note.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.number</code>.
     */
    public void setNumber(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.number</code>.
     */
    public String getNumber() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.delivery_date</code>.
     */
    public void setDeliveryDate(LocalDateTime value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.delivery_date</code>.
     */
    public LocalDateTime getDeliveryDate() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.from_address</code>.
     */
    public void setFromAddress(JSONB value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.from_address</code>.
     */
    public JSONB getFromAddress() {
        return (JSONB) get(7);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.to_address</code>.
     */
    public void setToAddress(JSONB value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.to_address</code>.
     */
    public JSONB getToAddress() {
        return (JSONB) get(8);
    }

    /**
     * Setter for
     * <code>public.goods_accompanying_note.transport_registration_number</code>.
     */
    public void setTransportRegistrationNumber(String value) {
        set(9, value);
    }

    /**
     * Getter for
     * <code>public.goods_accompanying_note.transport_registration_number</code>.
     */
    public String getTransportRegistrationNumber() {
        return (String) get(9);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.notes</code>.
     */
    public void setNotes(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.notes</code>.
     */
    public String getNotes() {
        return (String) get(10);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.items</code>.
     */
    public void setItems(JSONB value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.items</code>.
     */
    public JSONB getItems() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>public.goods_accompanying_note.delegate_id</code>.
     */
    public void setDelegateId(UUID value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.goods_accompanying_note.delegate_id</code>.
     */
    public UUID getDelegateId() {
        return (UUID) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row13<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, LocalDateTime, JSONB, JSONB, String, String, JSONB, UUID> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    @Override
    public Row13<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, LocalDateTime, JSONB, JSONB, String, String, JSONB, UUID> valuesRow() {
        return (Row13) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.NUMBER;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.DELIVERY_DATE;
    }

    @Override
    public Field<JSONB> field8() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.FROM_ADDRESS;
    }

    @Override
    public Field<JSONB> field9() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.TO_ADDRESS;
    }

    @Override
    public Field<String> field10() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.TRANSPORT_REGISTRATION_NUMBER;
    }

    @Override
    public Field<String> field11() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.NOTES;
    }

    @Override
    public Field<JSONB> field12() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.ITEMS;
    }

    @Override
    public Field<UUID> field13() {
        return GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE.DELEGATE_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getNumber();
    }

    @Override
    public LocalDateTime component7() {
        return getDeliveryDate();
    }

    @Override
    public JSONB component8() {
        return getFromAddress();
    }

    @Override
    public JSONB component9() {
        return getToAddress();
    }

    @Override
    public String component10() {
        return getTransportRegistrationNumber();
    }

    @Override
    public String component11() {
        return getNotes();
    }

    @Override
    public JSONB component12() {
        return getItems();
    }

    @Override
    public UUID component13() {
        return getDelegateId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getNumber();
    }

    @Override
    public LocalDateTime value7() {
        return getDeliveryDate();
    }

    @Override
    public JSONB value8() {
        return getFromAddress();
    }

    @Override
    public JSONB value9() {
        return getToAddress();
    }

    @Override
    public String value10() {
        return getTransportRegistrationNumber();
    }

    @Override
    public String value11() {
        return getNotes();
    }

    @Override
    public JSONB value12() {
        return getItems();
    }

    @Override
    public UUID value13() {
        return getDelegateId();
    }

    @Override
    public GoodsAccompanyingNoteRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value6(String value) {
        setNumber(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value7(LocalDateTime value) {
        setDeliveryDate(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value8(JSONB value) {
        setFromAddress(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value9(JSONB value) {
        setToAddress(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value10(String value) {
        setTransportRegistrationNumber(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value11(String value) {
        setNotes(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value12(JSONB value) {
        setItems(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord value13(UUID value) {
        setDelegateId(value);
        return this;
    }

    @Override
    public GoodsAccompanyingNoteRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, LocalDateTime value7, JSONB value8, JSONB value9, String value10, String value11, JSONB value12, UUID value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached GoodsAccompanyingNoteRecord
     */
    public GoodsAccompanyingNoteRecord() {
        super(GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE);
    }

    /**
     * Create a detached, initialised GoodsAccompanyingNoteRecord
     */
    public GoodsAccompanyingNoteRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String number, LocalDateTime deliveryDate, JSONB fromAddress, JSONB toAddress, String transportRegistrationNumber, String notes, JSONB items, UUID delegateId) {
        super(GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setNumber(number);
        setDeliveryDate(deliveryDate);
        setFromAddress(fromAddress);
        setToAddress(toAddress);
        setTransportRegistrationNumber(transportRegistrationNumber);
        setNotes(notes);
        setItems(items);
        setDelegateId(delegateId);
    }
}
