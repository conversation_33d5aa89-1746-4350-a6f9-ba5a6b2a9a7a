/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.InvoiceRecord;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Invoice extends TableImpl<InvoiceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.invoice</code>
     */
    public static final Invoice INVOICE = new Invoice();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InvoiceRecord> getRecordType() {
        return InvoiceRecord.class;
    }

    /**
     * The column <code>public.invoice.id</code>.
     */
    public final TableField<InvoiceRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.invoice.create_time</code>.
     */
    public final TableField<InvoiceRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.invoice.update_time</code>.
     */
    public final TableField<InvoiceRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.invoice.deleted</code>.
     */
    public final TableField<InvoiceRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.invoice.owner_id</code>.
     */
    public final TableField<InvoiceRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.invoice.sales_order_id</code>.
     */
    public final TableField<InvoiceRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.invoice.customer_id</code>.
     */
    public final TableField<InvoiceRecord, UUID> CUSTOMER_ID = createField(DSL.name("customer_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.invoice.number</code>.
     */
    public final TableField<InvoiceRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.invoice.due_date</code>.
     */
    public final TableField<InvoiceRecord, LocalDateTime> DUE_DATE = createField(DSL.name("due_date"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>public.invoice.paid</code>.
     */
    public final TableField<InvoiceRecord, Boolean> PAID = createField(DSL.name("paid"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.invoice.sent_at</code>.
     */
    public final TableField<InvoiceRecord, LocalDateTime> SENT_AT = createField(DSL.name("sent_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.invoice.items</code>.
     */
    public final TableField<InvoiceRecord, JSONB> ITEMS = createField(DSL.name("items"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.invoice.proforma</code>.
     */
    public final TableField<InvoiceRecord, Boolean> PROFORMA = createField(DSL.name("proforma"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.invoice.notes</code>.
     */
    public final TableField<InvoiceRecord, String> NOTES = createField(DSL.name("notes"), SQLDataType.VARCHAR(5000), this, "");

    /**
     * The column <code>public.invoice.rendering_details</code>.
     */
    public final TableField<InvoiceRecord, JSONB> RENDERING_DETAILS = createField(DSL.name("rendering_details"), SQLDataType.JSONB, this, "");

    private Invoice(Name alias, Table<InvoiceRecord> aliased) {
        this(alias, aliased, null);
    }

    private Invoice(Name alias, Table<InvoiceRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.invoice</code> table reference
     */
    public Invoice(String alias) {
        this(DSL.name(alias), INVOICE);
    }

    /**
     * Create an aliased <code>public.invoice</code> table reference
     */
    public Invoice(Name alias) {
        this(alias, INVOICE);
    }

    /**
     * Create a <code>public.invoice</code> table reference
     */
    public Invoice() {
        this(DSL.name("invoice"), null);
    }

    public <O extends Record> Invoice(Table<O> child, ForeignKey<O, InvoiceRecord> key) {
        super(child, key, INVOICE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<InvoiceRecord> getPrimaryKey() {
        return Keys.INVOICE_PKEY;
    }

    @Override
    public List<UniqueKey<InvoiceRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.INVOICE_SALES_ORDER_ID_PROFORMA_KEY);
    }

    @Override
    public List<ForeignKey<InvoiceRecord, ?>> getReferences() {
        return Arrays.asList(Keys.INVOICE__INVOICE_OWNER_ID_FKEY, Keys.INVOICE__INVOICE_SALES_ORDER_ID_FKEY, Keys.INVOICE__INVOICE_CUSTOMER_ID_FKEY);
    }

    private transient Account _account;
    private transient SalesOrder _salesOrder;
    private transient Company _company;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.INVOICE__INVOICE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.INVOICE__INVOICE_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.INVOICE__INVOICE_CUSTOMER_ID_FKEY);

        return _company;
    }

    @Override
    public Invoice as(String alias) {
        return new Invoice(DSL.name(alias), this);
    }

    @Override
    public Invoice as(Name alias) {
        return new Invoice(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Invoice rename(String name) {
        return new Invoice(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Invoice rename(Name name) {
        return new Invoice(name, null);
    }

    // -------------------------------------------------------------------------
    // Row15 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, String, LocalDateTime, Boolean, LocalDateTime, JSONB, Boolean, String, JSONB> fieldsRow() {
        return (Row15) super.fieldsRow();
    }
}
