/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Notification;
import org.jooq.*;
import org.jooq.impl.UpdatableRecordImpl;

import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class NotificationRecord extends UpdatableRecordImpl<NotificationRecord> implements Record13<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, UUID, LocalDateTime, UUID, String, JSONB, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.notification.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.notification.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.notification.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.notification.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.notification.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.notification.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.notification.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.notification.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.notification.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.notification.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.notification.type</code>.
     */
    public void setType(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.notification.type</code>.
     */
    public String getType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.notification.section</code>.
     */
    public void setSection(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.notification.section</code>.
     */
    public String getSection() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.notification.target_entity_id</code>.
     */
    public void setTargetEntityId(UUID value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.notification.target_entity_id</code>.
     */
    public UUID getTargetEntityId() {
        return (UUID) get(7);
    }

    /**
     * Setter for <code>public.notification.resolved_at</code>.
     */
    public void setResolvedAt(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.notification.resolved_at</code>.
     */
    public LocalDateTime getResolvedAt() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>public.notification.resolved_by</code>.
     */
    public void setResolvedBy(UUID value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.notification.resolved_by</code>.
     */
    public UUID getResolvedBy() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>public.notification.triggered_by</code>.
     */
    public void setTriggeredBy(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.notification.triggered_by</code>.
     */
    public String getTriggeredBy() {
        return (String) get(10);
    }

    /**
     * Setter for <code>public.notification.details</code>.
     */
    public void setDetails(JSONB value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.notification.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>public.notification.target_user_id</code>.
     */
    public void setTargetUserId(UUID value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.notification.target_user_id</code>.
     */
    public UUID getTargetUserId() {
        return (UUID) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row13<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, UUID, LocalDateTime, UUID, String, JSONB, UUID> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    @Override
    public Row13<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, UUID, LocalDateTime, UUID, String, JSONB, UUID> valuesRow() {
        return (Row13) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Notification.NOTIFICATION.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Notification.NOTIFICATION.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Notification.NOTIFICATION.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Notification.NOTIFICATION.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return Notification.NOTIFICATION.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return Notification.NOTIFICATION.TYPE;
    }

    @Override
    public Field<String> field7() {
        return Notification.NOTIFICATION.SECTION;
    }

    @Override
    public Field<UUID> field8() {
        return Notification.NOTIFICATION.TARGET_ENTITY_ID;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return Notification.NOTIFICATION.RESOLVED_AT;
    }

    @Override
    public Field<UUID> field10() {
        return Notification.NOTIFICATION.RESOLVED_BY;
    }

    @Override
    public Field<String> field11() {
        return Notification.NOTIFICATION.TRIGGERED_BY;
    }

    @Override
    public Field<JSONB> field12() {
        return Notification.NOTIFICATION.DETAILS;
    }

    @Override
    public Field<UUID> field13() {
        return Notification.NOTIFICATION.TARGET_USER_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getType();
    }

    @Override
    public String component7() {
        return getSection();
    }

    @Override
    public UUID component8() {
        return getTargetEntityId();
    }

    @Override
    public LocalDateTime component9() {
        return getResolvedAt();
    }

    @Override
    public UUID component10() {
        return getResolvedBy();
    }

    @Override
    public String component11() {
        return getTriggeredBy();
    }

    @Override
    public JSONB component12() {
        return getDetails();
    }

    @Override
    public UUID component13() {
        return getTargetUserId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getType();
    }

    @Override
    public String value7() {
        return getSection();
    }

    @Override
    public UUID value8() {
        return getTargetEntityId();
    }

    @Override
    public LocalDateTime value9() {
        return getResolvedAt();
    }

    @Override
    public UUID value10() {
        return getResolvedBy();
    }

    @Override
    public String value11() {
        return getTriggeredBy();
    }

    @Override
    public JSONB value12() {
        return getDetails();
    }

    @Override
    public UUID value13() {
        return getTargetUserId();
    }

    @Override
    public NotificationRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public NotificationRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public NotificationRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public NotificationRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public NotificationRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public NotificationRecord value6(String value) {
        setType(value);
        return this;
    }

    @Override
    public NotificationRecord value7(String value) {
        setSection(value);
        return this;
    }

    @Override
    public NotificationRecord value8(UUID value) {
        setTargetEntityId(value);
        return this;
    }

    @Override
    public NotificationRecord value9(LocalDateTime value) {
        setResolvedAt(value);
        return this;
    }

    @Override
    public NotificationRecord value10(UUID value) {
        setResolvedBy(value);
        return this;
    }

    @Override
    public NotificationRecord value11(String value) {
        setTriggeredBy(value);
        return this;
    }

    @Override
    public NotificationRecord value12(JSONB value) {
        setDetails(value);
        return this;
    }

    @Override
    public NotificationRecord value13(UUID value) {
        setTargetUserId(value);
        return this;
    }

    @Override
    public NotificationRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, String value7, UUID value8, LocalDateTime value9, UUID value10, String value11, JSONB value12, UUID value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached NotificationRecord
     */
    public NotificationRecord() {
        super(Notification.NOTIFICATION);
    }

    /**
     * Create a detached, initialised NotificationRecord
     */
    public NotificationRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String type, String section, UUID targetEntityId, LocalDateTime resolvedAt, UUID resolvedBy, String triggeredBy, JSONB details, UUID targetUserId) {
        super(Notification.NOTIFICATION);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setType(type);
        setSection(section);
        setTargetEntityId(targetEntityId);
        setResolvedAt(resolvedAt);
        setResolvedBy(resolvedBy);
        setTriggeredBy(triggeredBy);
        setDetails(details);
        setTargetUserId(targetUserId);
    }
}
