/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ServiceTemplate;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServiceTemplateRecord extends UpdatableRecordImpl<ServiceTemplateRecord> implements Record12<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, BigDecimal, String, Integer, Currency, Integer, Currency> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.service_template.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.service_template.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.service_template.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.service_template.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.service_template.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.service_template.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.service_template.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.service_template.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.service_template.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.service_template.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.service_template.name</code>.
     */
    public void setName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.service_template.name</code>.
     */
    public String getName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.service_template.vat_rate</code>.
     */
    public void setVatRate(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.service_template.vat_rate</code>.
     */
    public BigDecimal getVatRate() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>public.service_template.measurement_unit</code>.
     */
    public void setMeasurementUnit(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.service_template.measurement_unit</code>.
     */
    public String getMeasurementUnit() {
        return (String) get(7);
    }

    /**
     * Setter for <code>public.service_template.sell_price_amount</code>.
     */
    public void setSellPriceAmount(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.service_template.sell_price_amount</code>.
     */
    public Integer getSellPriceAmount() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>public.service_template.sell_price_currency</code>.
     */
    public void setSellPriceCurrency(Currency value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.service_template.sell_price_currency</code>.
     */
    public Currency getSellPriceCurrency() {
        return (Currency) get(9);
    }

    /**
     * Setter for <code>public.service_template.cost_amount</code>.
     */
    public void setCostAmount(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.service_template.cost_amount</code>.
     */
    public Integer getCostAmount() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>public.service_template.cost_currency</code>.
     */
    public void setCostCurrency(Currency value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.service_template.cost_currency</code>.
     */
    public Currency getCostCurrency() {
        return (Currency) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row12<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, BigDecimal, String, Integer, Currency, Integer, Currency> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    @Override
    public Row12<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, BigDecimal, String, Integer, Currency, Integer, Currency> valuesRow() {
        return (Row12) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ServiceTemplate.SERVICE_TEMPLATE.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return ServiceTemplate.SERVICE_TEMPLATE.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return ServiceTemplate.SERVICE_TEMPLATE.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return ServiceTemplate.SERVICE_TEMPLATE.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return ServiceTemplate.SERVICE_TEMPLATE.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return ServiceTemplate.SERVICE_TEMPLATE.NAME;
    }

    @Override
    public Field<BigDecimal> field7() {
        return ServiceTemplate.SERVICE_TEMPLATE.VAT_RATE;
    }

    @Override
    public Field<String> field8() {
        return ServiceTemplate.SERVICE_TEMPLATE.MEASUREMENT_UNIT;
    }

    @Override
    public Field<Integer> field9() {
        return ServiceTemplate.SERVICE_TEMPLATE.SELL_PRICE_AMOUNT;
    }

    @Override
    public Field<Currency> field10() {
        return ServiceTemplate.SERVICE_TEMPLATE.SELL_PRICE_CURRENCY;
    }

    @Override
    public Field<Integer> field11() {
        return ServiceTemplate.SERVICE_TEMPLATE.COST_AMOUNT;
    }

    @Override
    public Field<Currency> field12() {
        return ServiceTemplate.SERVICE_TEMPLATE.COST_CURRENCY;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getName();
    }

    @Override
    public BigDecimal component7() {
        return getVatRate();
    }

    @Override
    public String component8() {
        return getMeasurementUnit();
    }

    @Override
    public Integer component9() {
        return getSellPriceAmount();
    }

    @Override
    public Currency component10() {
        return getSellPriceCurrency();
    }

    @Override
    public Integer component11() {
        return getCostAmount();
    }

    @Override
    public Currency component12() {
        return getCostCurrency();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getName();
    }

    @Override
    public BigDecimal value7() {
        return getVatRate();
    }

    @Override
    public String value8() {
        return getMeasurementUnit();
    }

    @Override
    public Integer value9() {
        return getSellPriceAmount();
    }

    @Override
    public Currency value10() {
        return getSellPriceCurrency();
    }

    @Override
    public Integer value11() {
        return getCostAmount();
    }

    @Override
    public Currency value12() {
        return getCostCurrency();
    }

    @Override
    public ServiceTemplateRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value6(String value) {
        setName(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value7(BigDecimal value) {
        setVatRate(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value8(String value) {
        setMeasurementUnit(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value9(Integer value) {
        setSellPriceAmount(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value10(Currency value) {
        setSellPriceCurrency(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value11(Integer value) {
        setCostAmount(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord value12(Currency value) {
        setCostCurrency(value);
        return this;
    }

    @Override
    public ServiceTemplateRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, BigDecimal value7, String value8, Integer value9, Currency value10, Integer value11, Currency value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ServiceTemplateRecord
     */
    public ServiceTemplateRecord() {
        super(ServiceTemplate.SERVICE_TEMPLATE);
    }

    /**
     * Create a detached, initialised ServiceTemplateRecord
     */
    public ServiceTemplateRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String name, BigDecimal vatRate, String measurementUnit, Integer sellPriceAmount, Currency sellPriceCurrency, Integer costAmount, Currency costCurrency) {
        super(ServiceTemplate.SERVICE_TEMPLATE);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setName(name);
        setVatRate(vatRate);
        setMeasurementUnit(measurementUnit);
        setSellPriceAmount(sellPriceAmount);
        setSellPriceCurrency(sellPriceCurrency);
        setCostAmount(costAmount);
        setCostCurrency(costCurrency);
    }
}
