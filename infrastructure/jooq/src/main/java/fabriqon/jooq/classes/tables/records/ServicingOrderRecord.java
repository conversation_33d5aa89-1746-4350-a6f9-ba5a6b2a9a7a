/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ServicingOrder;
import org.jooq.*;
import org.jooq.impl.UpdatableRecordImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServicingOrderRecord extends UpdatableRecordImpl<ServicingOrderRecord> implements Record22<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, String, BigDecimal, Integer, String, JSONB, JSONB, Object, UUID, JSONB, UUID, String, String, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.servicing_order.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.servicing_order.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.servicing_order.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.servicing_order.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.servicing_order.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.servicing_order.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.servicing_order.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.servicing_order.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.servicing_order.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.servicing_order.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.servicing_order.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.servicing_order.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.servicing_order.number</code>.
     */
    public void setNumber(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.servicing_order.number</code>.
     */
    public String getNumber() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.servicing_order.execution_deadline</code>.
     */
    public void setExecutionDeadline(LocalDateTime value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.servicing_order.execution_deadline</code>.
     */
    public LocalDateTime getExecutionDeadline() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>public.servicing_order.status</code>.
     */
    public void setStatus(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.servicing_order.status</code>.
     */
    public String getStatus() {
        return (String) get(8);
    }

    /**
     * Setter for <code>public.servicing_order.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.servicing_order.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(9);
    }

    /**
     * Setter for <code>public.servicing_order.ranking</code>.
     */
    public void setRanking(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.servicing_order.ranking</code>.
     */
    public Integer getRanking() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>public.servicing_order.notes</code>.
     */
    public void setNotes(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.servicing_order.notes</code>.
     */
    public String getNotes() {
        return (String) get(11);
    }

    /**
     * Setter for <code>public.servicing_order.materials</code>.
     */
    public void setMaterials(JSONB value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.servicing_order.materials</code>.
     */
    public JSONB getMaterials() {
        return (JSONB) get(12);
    }

    /**
     * Setter for <code>public.servicing_order.operations</code>.
     */
    public void setOperations(JSONB value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.servicing_order.operations</code>.
     */
    public JSONB getOperations() {
        return (JSONB) get(13);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(14, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(14);
    }

    /**
     * Setter for <code>public.servicing_order.service_id</code>.
     */
    public void setServiceId(UUID value) {
        set(15, value);
    }

    /**
     * Getter for <code>public.servicing_order.service_id</code>.
     */
    public UUID getServiceId() {
        return (UUID) get(15);
    }

    /**
     * Setter for <code>public.servicing_order.servicing_costs</code>.
     */
    public void setServicingCosts(JSONB value) {
        set(16, value);
    }

    /**
     * Getter for <code>public.servicing_order.servicing_costs</code>.
     */
    public JSONB getServicingCosts() {
        return (JSONB) get(16);
    }

    /**
     * Setter for <code>public.servicing_order.assigned_to</code>.
     */
    public void setAssignedTo(UUID value) {
        set(17, value);
    }

    /**
     * Getter for <code>public.servicing_order.assigned_to</code>.
     */
    public UUID getAssignedTo() {
        return (UUID) get(17);
    }

    /**
     * Setter for <code>public.servicing_order.base64_client_signature</code>.
     */
    public void setBase64ClientSignature(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>public.servicing_order.base64_client_signature</code>.
     */
    public String getBase64ClientSignature() {
        return (String) get(18);
    }

    /**
     * Setter for <code>public.servicing_order.client_representative</code>.
     */
    public void setClientRepresentative(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>public.servicing_order.client_representative</code>.
     */
    public String getClientRepresentative() {
        return (String) get(19);
    }

    /**
     * Setter for <code>public.servicing_order.base64_worker_signature</code>.
     */
    public void setBase64WorkerSignature(String value) {
        set(20, value);
    }

    /**
     * Getter for <code>public.servicing_order.base64_worker_signature</code>.
     */
    public String getBase64WorkerSignature() {
        return (String) get(20);
    }

    /**
     * Setter for <code>public.servicing_order.blocked_reason</code>.
     */
    public void setBlockedReason(String value) {
        set(21, value);
    }

    /**
     * Getter for <code>public.servicing_order.blocked_reason</code>.
     */
    public String getBlockedReason() {
        return (String) get(21);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record22 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row22<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, String, BigDecimal, Integer, String, JSONB, JSONB, Object, UUID, JSONB, UUID, String, String, String, String> fieldsRow() {
        return (Row22) super.fieldsRow();
    }

    @Override
    public Row22<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, String, BigDecimal, Integer, String, JSONB, JSONB, Object, UUID, JSONB, UUID, String, String, String, String> valuesRow() {
        return (Row22) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ServicingOrder.SERVICING_ORDER.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return ServicingOrder.SERVICING_ORDER.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return ServicingOrder.SERVICING_ORDER.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return ServicingOrder.SERVICING_ORDER.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return ServicingOrder.SERVICING_ORDER.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return ServicingOrder.SERVICING_ORDER.SALES_ORDER_ID;
    }

    @Override
    public Field<String> field7() {
        return ServicingOrder.SERVICING_ORDER.NUMBER;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return ServicingOrder.SERVICING_ORDER.EXECUTION_DEADLINE;
    }

    @Override
    public Field<String> field9() {
        return ServicingOrder.SERVICING_ORDER.STATUS;
    }

    @Override
    public Field<BigDecimal> field10() {
        return ServicingOrder.SERVICING_ORDER.QUANTITY;
    }

    @Override
    public Field<Integer> field11() {
        return ServicingOrder.SERVICING_ORDER.RANKING;
    }

    @Override
    public Field<String> field12() {
        return ServicingOrder.SERVICING_ORDER.NOTES;
    }

    @Override
    public Field<JSONB> field13() {
        return ServicingOrder.SERVICING_ORDER.MATERIALS;
    }

    @Override
    public Field<JSONB> field14() {
        return ServicingOrder.SERVICING_ORDER.OPERATIONS;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Field<Object> field15() {
        return ServicingOrder.SERVICING_ORDER.TEXT_SEARCH;
    }

    @Override
    public Field<UUID> field16() {
        return ServicingOrder.SERVICING_ORDER.SERVICE_ID;
    }

    @Override
    public Field<JSONB> field17() {
        return ServicingOrder.SERVICING_ORDER.SERVICING_COSTS;
    }

    @Override
    public Field<UUID> field18() {
        return ServicingOrder.SERVICING_ORDER.ASSIGNED_TO;
    }

    @Override
    public Field<String> field19() {
        return ServicingOrder.SERVICING_ORDER.BASE64_CLIENT_SIGNATURE;
    }

    @Override
    public Field<String> field20() {
        return ServicingOrder.SERVICING_ORDER.CLIENT_REPRESENTATIVE;
    }

    @Override
    public Field<String> field21() {
        return ServicingOrder.SERVICING_ORDER.BASE64_WORKER_SIGNATURE;
    }

    @Override
    public Field<String> field22() {
        return ServicingOrder.SERVICING_ORDER.BLOCKED_REASON;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getSalesOrderId();
    }

    @Override
    public String component7() {
        return getNumber();
    }

    @Override
    public LocalDateTime component8() {
        return getExecutionDeadline();
    }

    @Override
    public String component9() {
        return getStatus();
    }

    @Override
    public BigDecimal component10() {
        return getQuantity();
    }

    @Override
    public Integer component11() {
        return getRanking();
    }

    @Override
    public String component12() {
        return getNotes();
    }

    @Override
    public JSONB component13() {
        return getMaterials();
    }

    @Override
    public JSONB component14() {
        return getOperations();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object component15() {
        return getTextSearch();
    }

    @Override
    public UUID component16() {
        return getServiceId();
    }

    @Override
    public JSONB component17() {
        return getServicingCosts();
    }

    @Override
    public UUID component18() {
        return getAssignedTo();
    }

    @Override
    public String component19() {
        return getBase64ClientSignature();
    }

    @Override
    public String component20() {
        return getClientRepresentative();
    }

    @Override
    public String component21() {
        return getBase64WorkerSignature();
    }

    @Override
    public String component22() {
        return getBlockedReason();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getSalesOrderId();
    }

    @Override
    public String value7() {
        return getNumber();
    }

    @Override
    public LocalDateTime value8() {
        return getExecutionDeadline();
    }

    @Override
    public String value9() {
        return getStatus();
    }

    @Override
    public BigDecimal value10() {
        return getQuantity();
    }

    @Override
    public Integer value11() {
        return getRanking();
    }

    @Override
    public String value12() {
        return getNotes();
    }

    @Override
    public JSONB value13() {
        return getMaterials();
    }

    @Override
    public JSONB value14() {
        return getOperations();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object value15() {
        return getTextSearch();
    }

    @Override
    public UUID value16() {
        return getServiceId();
    }

    @Override
    public JSONB value17() {
        return getServicingCosts();
    }

    @Override
    public UUID value18() {
        return getAssignedTo();
    }

    @Override
    public String value19() {
        return getBase64ClientSignature();
    }

    @Override
    public String value20() {
        return getClientRepresentative();
    }

    @Override
    public String value21() {
        return getBase64WorkerSignature();
    }

    @Override
    public String value22() {
        return getBlockedReason();
    }

    @Override
    public ServicingOrderRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value6(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value7(String value) {
        setNumber(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value8(LocalDateTime value) {
        setExecutionDeadline(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value9(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value10(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value11(Integer value) {
        setRanking(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value12(String value) {
        setNotes(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value13(JSONB value) {
        setMaterials(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value14(JSONB value) {
        setOperations(value);
        return this;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public ServicingOrderRecord value15(Object value) {
        setTextSearch(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value16(UUID value) {
        setServiceId(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value17(JSONB value) {
        setServicingCosts(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value18(UUID value) {
        setAssignedTo(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value19(String value) {
        setBase64ClientSignature(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value20(String value) {
        setClientRepresentative(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value21(String value) {
        setBase64WorkerSignature(value);
        return this;
    }

    @Override
    public ServicingOrderRecord value22(String value) {
        setBlockedReason(value);
        return this;
    }

    @Override
    public ServicingOrderRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, String value7, LocalDateTime value8, String value9, BigDecimal value10, Integer value11, String value12, JSONB value13, JSONB value14, Object value15, UUID value16, JSONB value17, UUID value18, String value19, String value20, String value21, String value22) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        value21(value21);
        value22(value22);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ServicingOrderRecord
     */
    public ServicingOrderRecord() {
        super(ServicingOrder.SERVICING_ORDER);
    }

    /**
     * Create a detached, initialised ServicingOrderRecord
     */
    public ServicingOrderRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID salesOrderId, String number, LocalDateTime executionDeadline, String status, BigDecimal quantity, Integer ranking, String notes, JSONB materials, JSONB operations, Object textSearch, UUID serviceId, JSONB servicingCosts, UUID assignedTo, String base64ClientSignature, String clientRepresentative, String base64WorkerSignature, String blockedReason) {
        super(ServicingOrder.SERVICING_ORDER);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setSalesOrderId(salesOrderId);
        setNumber(number);
        setExecutionDeadline(executionDeadline);
        setStatus(status);
        setQuantity(quantity);
        setRanking(ranking);
        setNotes(notes);
        setMaterials(materials);
        setOperations(operations);
        setTextSearch(textSearch);
        setServiceId(serviceId);
        setServicingCosts(servicingCosts);
        setAssignedTo(assignedTo);
        setBase64ClientSignature(base64ClientSignature);
        setClientRepresentative(clientRepresentative);
        setBase64WorkerSignature(base64WorkerSignature);
        setBlockedReason(blockedReason);
    }
}
