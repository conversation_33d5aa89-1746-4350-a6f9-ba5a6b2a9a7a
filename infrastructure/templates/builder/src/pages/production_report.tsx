import BottomInfo from 'components/BottomInfo';
import Header from 'components/Header';
import Page from 'components/Page';
import RandomInfo from 'components/RandomInfo';
import React, {FC, useEffect, useState} from 'react';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';

const ProductionReport: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <Page fileName='production_report'>
      <Header>
        <Header.Title>{`{{labels.production_report_title}}`}</Header.Title>
        <Header.Subtitle>{`{{labels.number}}: {{order.number}}`}</Header.Subtitle>
        <TopInfo>
          <TopInfo.Info label={`{{labels.date}}`}>{`{{order.date}}`}</TopInfo.Info>
          <TopInfo.Info label={`{{labels.entity}}`}>{`{{supplierName}}`}</TopInfo.Info>
        </TopInfo>
      </Header>
      <RandomInfo>
        <div className='flex gap-4'>
          <div className='text-[10px] font-light'>{`{{labels.product}}`}:</div>
          <div className='text-[10px] font-semibold'>{`{{product.name}}`}</div>
        </div>
        <div className='flex gap-4'>
          <div className='text-[10px] font-light'>{`{{labels.code}}`}:</div>
          <div className='text-[10px] font-semibold'>{`{{product.code}}`}</div>
        </div>
        {`{{#product.customProduct}}`}
        <div className='flex gap-4'>
          <div className='text-[10px] font-light'>{`{{labels.customProduct}}`}:</div>
          <div className='text-[10px] font-semibold'>{`{{product.customProduct}}`}</div>
        </div>
        {`{{/product.customProduct}}`}
        <div className='flex gap-4'>
          <div className='text-[10px] font-light'>{`{{labels.quantity}}`}:</div>
          <div className='text-[10px] font-semibold'>{`{{product.quantity}} {{product.measurementUnit}}`}</div>
        </div>
      </RandomInfo>
      <Table title={`{{labels.material_consumption}}`}>
        <Table.Header>
          <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
          <Table.Head>{`{{labels.material_name}}`}</Table.Head>
          <Table.Head>{`{{labels.code}}`}</Table.Head>
          <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.standard_consumption}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.actual_consumption}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.unit_cost}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.total_cost}}`}</Table.Head>
        </Table.Header>
        <Table.Body>
          {`{{#materialConsumption}}`}
          <Table.Row>
            <Table.Cell>{`{{index}}`}</Table.Cell>
            <Table.Cell>
              <span
                dangerouslySetInnerHTML={{
                  __html: `<span style="padding-left: calc({{level}} * 20px);">{{name}}</span>`,
                }}
              />
            </Table.Cell>
            <Table.Cell>{`{{code}}`}</Table.Cell>
            <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{standardConsumption}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{actualConsumption}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{unitCost}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{totalCost}}`}</Table.Cell>
          </Table.Row>
          {`{{/materialConsumption}}`}
        </Table.Body>
      </Table>
      <BottomInfo>
        <BottomInfo.Container>
          <BottomInfo.Info
            label={`{{labels.material_costs_total}}`} labelClassName='font-semibold' textSize='text-xs'
            valueClassName='font-semibold'>{`{{report.totalMaterialsCosts}}`}</BottomInfo.Info>
        </BottomInfo.Container>
      </BottomInfo>
      <Table title={`{{labels.labor_and_equipment_costs}}`} withoutCurrency>
        <Table.Header>
          <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
          <Table.Head>{`{{labels.task_name}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.standard_duration}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.employee_hourly_cost}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.employee_total_cost}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.workstation_hourly_cost}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.workstation_total_cost}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.overhead_cost}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.total_cost}}`}</Table.Head>
        </Table.Header>
        <Table.Body>
          {`{{#laborAndEquipmentCosts}}`}
          <Table.Row>
            <Table.Cell>{`{{index}}`}</Table.Cell>
            <Table.Cell>{`{{taskName}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{standardDuration}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{employeeHourlyCost}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{employeeTotalCost}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{workstationHourlyCost}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{workstationTotalCost}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{overheadCost}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{totalCost}}`}</Table.Cell>
          </Table.Row>
          {`{{/laborAndEquipmentCosts}}`}
        </Table.Body>
      </Table>
      <BottomInfo>
        <BottomInfo.Container>
          <BottomInfo.Info
            label={`{{labels.labor_costs_total}}`} labelClassName='font-semibold' textSize='text-xs'
            valueClassName='font-semibold'>{`{{report.totalLaborCosts}}`}</BottomInfo.Info>
          <BottomInfo.Info
            label={`{{labels.equipment_costs_total}}`} labelClassName='font-semibold' textSize='text-xs'
            valueClassName='font-semibold'>{`{{report.totalWorkstationCosts}}`}</BottomInfo.Info>
        </BottomInfo.Container>
      </BottomInfo>
      <BottomInfo>
        <BottomInfo.Container>
          <BottomInfo.Info label={`{{labels.production_cost_summary}}`} labelClassName='font-semibold'
                           labelTextSize='text-sm'/>
          <BottomInfo.Info
            label={`{{labels.raw_materials_and_direct_materials}}`}>{`{{report.totalMaterialCosts}}`}</BottomInfo.Info>
          <BottomInfo.Info label={`{{labels.direct_labor}}`}>{`{{report.totalLaborCosts}}`}</BottomInfo.Info>
          <BottomInfo.Info label={`{{labels.equipment_cost}}`}>{`{{report.totalWorkstationCosts}}`}</BottomInfo.Info>
          <BottomInfo.Info label={`{{labels.total_direct_cost}}`}>{`{{report.totalDirectCosts}}`}</BottomInfo.Info>
          <BottomInfo.Info className='border-b'
                           label={`{{labels.total_indirect_cost}}`}>{`{{report.totalIndirectCosts}}`}</BottomInfo.Info>
          <BottomInfo.Info label={`{{labels.total_cost}}`} labelClassName='!text-sm pt-4 font-semibold'
                           valueClassName='!text-sm pt-4 font-semibold'>{`{{report.totalCosts}}`}</BottomInfo.Info>
        </BottomInfo.Container>
      </BottomInfo>
    </Page>
  );
};

export default ProductionReport;
