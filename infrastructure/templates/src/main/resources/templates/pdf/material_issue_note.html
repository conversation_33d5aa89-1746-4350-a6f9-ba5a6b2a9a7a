<html>
  <head>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=block" rel="stylesheet"></link>
    <style>*,::before,::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

*,::before,::after {
  box-sizing: border-box; 
  border-width: 0; 
  border-style: solid; 
  border-color: #e5e7eb; 
}::before,::after {
  --tw-content: '';
}



html,:host {
  line-height: 1.5; 
  -webkit-text-size-adjust: 100%; 
  -moz-tab-size: 4; 
  -o-tab-size: 4;
     tab-size: 4; 
  font-family: Inter, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; 
  font-feature-settings: normal; 
  font-variation-settings: normal; 
  -webkit-tap-highlight-color: transparent; 
}



body {
  margin: 0; 
  line-height: inherit; 
}



hr {
  height: 0; 
  color: inherit; 
  border-top-width: 1px; 
}



abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}



h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}



a {
  color: inherit;
  text-decoration: inherit;
}



b,
strong {
  font-weight: bolder;
}



code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; 
  font-feature-settings: normal; 
  font-variation-settings: normal; 
  font-size: 1em; 
}



small {
  font-size: 80%;
}



sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}



table {
  text-indent: 0; 
  border-color: inherit; 
  border-collapse: collapse; 
}



button,
input,
optgroup,
select,
textarea {
  font-family: inherit; 
  font-feature-settings: inherit; 
  font-variation-settings: inherit; 
  font-size: 100%; 
  font-weight: inherit; 
  line-height: inherit; 
  letter-spacing: inherit; 
  color: inherit; 
  margin: 0; 
  padding: 0; 
}



button,
select {
  text-transform: none;
}



button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; 
  background-color: transparent; 
  background-image: none; 
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}



progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button,::-webkit-outer-spin-button {
  height: auto;
}



[type='search'] {
  -webkit-appearance: textfield; 
  outline-offset: -2px; 
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button; 
  font: inherit; 
}



summary {
  display: list-item;
}



blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}


dialog {
  padding: 0;
}



textarea {
  resize: vertical;
}



input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; 
  color: #9ca3af; 
}

input::placeholder,
textarea::placeholder {
  opacity: 1; 
  color: #9ca3af; 
}



button,
[role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}



img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; 
  vertical-align: middle; 
}



img,
video {
  max-width: 100%;
  height: auto;
}


[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}.container {
  width: 100%;
}
@media (min-width: 640px) {.container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {.container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {.container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {.container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {.container {
    max-width: 1536px;
  }
}.absolute {
  position: absolute;
}.left-1\/2 {
  left: 50%;
}.right-0 {
  right: 0px;
}.top-0 {
  top: 0px;
}.top-0\.5 {
  top: 0.125rem;
}.mx-20 {
  margin-left: 5rem;
  margin-right: 5rem;
}.mx-auto {
  margin-left: auto;
  margin-right: auto;
}.mb-1 {
  margin-bottom: 0.25rem;
}.mb-2 {
  margin-bottom: 0.5rem;
}.mb-4 {
  margin-bottom: 1rem;
}.mt-4 {
  margin-top: 1rem;
}.block {
  display: block;
}.inline {
  display: inline;
}.flex {
  display: flex;
}.table {
  display: table;
}.grid {
  display: grid;
}.size-8 {
  width: 2rem;
  height: 2rem;
}.h-12 {
  height: 3rem;
}.h-24 {
  height: 6rem;
}.min-h-screen {
  min-height: 100vh;
}.w-full {
  width: 100%;
}.max-w-96 {
  max-width: 24rem;
}.max-w-\[50vw\] {
  max-width: 50vw;
}.grow {
  flex-grow: 1;
}.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}.flex-col {
  flex-direction: column;
}.items-end {
  align-items: flex-end;
}.items-center {
  align-items: center;
}.justify-end {
  justify-content: flex-end;
}.justify-center {
  justify-content: center;
}.justify-between {
  justify-content: space-between;
}.gap-1 {
  gap: 0.25rem;
}.gap-16 {
  gap: 4rem;
}.gap-2 {
  gap: 0.5rem;
}.gap-20 {
  gap: 5rem;
}.gap-4 {
  gap: 1rem;
}.gap-6 {
  gap: 1.5rem;
}.gap-8 {
  gap: 2rem;
}.divide-y >:not([hidden]) ~:not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}.whitespace-pre {
  white-space: pre;
}.rounded {
  border-radius: 0.25rem;
}.rounded-lg {
  border-radius: 0.5rem;
}.border {
  border-width: 1px;
}.border-b {
  border-bottom-width: 1px;
}.border-t {
  border-top-width: 1px;
}.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}.from-indigo-500 {
  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}.pb-1 {
  padding-bottom: 0.25rem;
}.pb-2 {
  padding-bottom: 0.5rem;
}.pb-4 {
  padding-bottom: 1rem;
}.pl-16 {
  padding-left: 4rem;
}.pl-4 {
  padding-left: 1rem;
}.pt-4 {
  padding-top: 1rem;
}.text-left {
  text-align: left;
}.text-center {
  text-align: center;
}.text-right {
  text-align: right;
}.\!text-sm {
  font-size: 0.875rem!important;
  line-height: 1.25rem!important;
}.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}.text-\[10px\] {
  font-size: 10px;
}.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}.font-bold {
  font-weight: 700;
}.font-light {
  font-weight: 300;
}.font-medium {
  font-weight: 500;
}.font-semibold {
  font-weight: 600;
}.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.duration-300 {
  transition-duration: 300ms;
}.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}.not-last-of-type\:border-b:not(:last-of-type) {
  border-bottom-width: 1px;
}
@media (min-width: 640px) {.sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 768px) {.md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

</style>
  </head>
  <body>
    <div>
      <div class="flex min-h-screen flex-col gap-8">
        <div class="flex flex-col divide-y">
          <div class="flex flex-col pb-4">
            <div class="font-semibold text-2xl">{{labels.material_issue_title}}</div>
            <div class="pb-4 font-medium text-xs">{{labels.number}}: {{note.number}}</div>
            <div class="flex gap-16">
              <div class="flex flex-col font-medium gap-1">
                <div class="text-[10px]">{{labels.date}}</div>
                <div class="text-xs">{{note.date}}</div>
              </div>
              <div class="flex flex-col font-medium gap-1">
                <div class="text-[10px]">{{labels.manufacturing_order}}</div>
                <div class="text-xs">{{note.manufacturingOrderNumber}}</div>
              </div>
              <div class="flex flex-col font-medium gap-1">
                <div class="text-[10px]">{{labels.product_name}}</div>
                <div class="text-xs">{{note.productName}}</div>
              </div>
              <div class="flex flex-col font-medium gap-1">
                <div class="text-[10px]">{{labels.product_sku}}</div>
                <div class="text-xs">{{note.productSku}}</div>
              </div>
              <div class="flex flex-col font-medium gap-1">
                <div class="text-[10px]">{{labels.product_quantity}}</div>
                <div class="text-xs">{{note.productQuantity}}</div>
              </div>
              <div class="flex flex-col font-medium gap-1">
                <div class="text-[10px]">{{labels.business_unit}}</div>
                <div class="text-xs">{{note.businessUnit}}</div>
              </div>
            </div>
{{#base64Logo}}
            <img alt="Logo Icon" class="absolute right-0 top-0 h-12" src="data:image/png;base64,{{base64Logo}}"></img>
{{/base64Logo}}
          </div>
          <div class="py-4">
            <div class="flex items-center justify-end gap-4 pb-4">
              <div class="text-xs">{{labels.currency}}:</div>
              <div class="font-semibold text-xs">{{currency}}</div>
            </div>
            <table class="w-full">
              <thead>
                <tr class="not-last-of-type:border-b">
                  <th class="font-light px-2 text-[10px] text-left">{{labels.number_abbreviation}}</th>
                  <th class="font-light px-2 text-[10px] text-left">{{labels.material_issue_item_name}}</th>
                  <th class="font-light px-2 text-[10px] text-left">{{labels.measurement_unit_abbreviation}}</th>
                  <th class="font-light px-2 text-[10px] text-right">{{labels.needed_quantity}}</th>
                  <th class="font-light px-2 text-[10px] text-right">{{labels.used_quantity}}</th>
                  <th class="font-light px-2 text-[10px] text-right">{{labels.unit_price}}</th>
                  <th class="font-light px-2 text-[10px] text-right">{{labels.value}}</th>
                </tr>
              </thead>
              <tbody class="text-center text-xs">
{{#note.materials}}
                <tr class="not-last-of-type:border-b">
                  <td class="py-4 px-2 text-left">{{index}}</td>
                  <td class="py-4 px-2 text-left">{{name}}</td>
                  <td class="py-4 px-2 text-left">{{measurementUnit}}</td>
                  <td class="py-4 px-2 text-right">{{neededQuantity}}</td>
                  <td class="py-4 px-2 text-right">{{usedQuantity}}</td>
                  <td class="py-4 px-2 text-right">{{price}}</td>
                  <td class="py-4 px-2 text-right">{{value}}</td>
                </tr>
{{/note.materials}}
              </tbody>
            </table>
          </div>
          <div class="flex justify-end py-4">
            <tr>
              <td class="pb-2 text-xs !text-sm">{{labels.total_value}}</td>
              <td class="pb-2 pl-16 font-medium text-right text-xs !text-sm">{{note.totalValue}}</td>
            </tr>
          </div>
        </div>
        <div class="grow"></div>
        <div class="mx-20 mb-4 flex justify-between gap-20 items-end`">
          <div class="flex w-full max-w-96 flex-col items-center">
            <div class="font-medium text-center text-sm">{{note.inventory_manager}}</div>
            <div class="border-t border-black flex w-full justify-center text-xs">{{labels.inventory_manager}}</div>
          </div>
          <div class="flex w-full max-w-96 flex-col items-center">
            <div class="font-medium text-center text-sm">{{note.receiver_person}}</div>
            <div class="border-t border-black flex w-full justify-center text-xs">{{labels.receiver_person}}</div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
