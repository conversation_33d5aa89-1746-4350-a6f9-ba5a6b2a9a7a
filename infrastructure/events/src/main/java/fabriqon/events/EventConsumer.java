package fabriqon.events;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;

@Slf4j
public abstract class EventConsumer<T extends Event> {

    final Class<T> persistentClass;

    @SuppressWarnings("unchecked")
    public EventConsumer() {
        this.persistentClass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
    }

    public void processEvent(T event) {
        log.info("processing event [{}] by [{}]", event.name(), this.getClass().getSimpleName());
        process(event);
    }

    public abstract void process(T event);

}
