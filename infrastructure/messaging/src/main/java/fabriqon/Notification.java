package fabriqon;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

public record Notification (
         UUID id,
         Instant createTime,
         Instant updateTime,
         boolean deleted,

         UUID ownerId,
         Type type,
         Section section,
         UUID targetEntityId,
         LocalDateTime resolvedAt,
         UUID resolvedBy,
         String triggeredBy,
         Map<String, Object> details,
         UUID targetUserId
) {

    public enum Section { SALES, MANUFACTURING_TASK, PURCHASES, CUSTOMERS, MANUFACTURING, SERVICING, NOTIFICATION }

    public enum Type { UPDATE, ISSUE, COMMENT }

}
