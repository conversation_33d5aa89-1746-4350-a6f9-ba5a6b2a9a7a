package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Tuple;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.*;

public class ManufacturingOrdersSubassemblyTests extends IntegrationTest {

    @Test
    public void manufactureProductWithSubassemblyAndCheckInventory() throws Exception {
        Account account = setup
                .account()
                .category().withName("category for product and subassemblies").create().account()
                .location().create().account()
                .supplier().create().account();

        var defaultInventoryUnitId = account.inventory().defaultInventoryUnit();

        //create the raw materials
        var rawMaterialId1 = account.good()
                .withName("material_1")
                .withCategory(account.category().categoryId)
                .create().goodId;
        account.inventory().withGood(rawMaterialId1).withQuantity(BigDecimal.TEN).fill();

        var rawMaterialId2 = account.good()
                .withName("material_2")
                .withCategory(account.category().categoryId)
                .create().goodId;
        account.inventory().withGood(rawMaterialId2).withQuantity(BigDecimal.TEN).fill();

        var subassemblyId = account.good()
                .withName("subassembly").withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(
                        List.of(UUID.fromString(rawMaterialId1), UUID.fromString(rawMaterialId2)),
                        BigDecimal.ONE, false,  true, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        var productId = account.good()
                .withName("product").withCategory(account.category().categoryId)
                .withMaterials(List.of(
                        new RequiredMaterial(List.of(UUID.fromString(rawMaterialId1)), BigDecimal.ONE, false,  false, false, BigDecimal.ZERO, null, null),
                        new RequiredMaterial(List.of(UUID.fromString(subassemblyId)), BigDecimal.ONE, false,  false, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        var productMOId = account.manufacturing().orderManufacturing(productId, BigDecimal.ONE).manufacturingOrderId;

        var allOrders = account.manufacturing().list();
        assertEquals("Expected 2 manufacturing orders (product + automatic subassembly)", 2, allOrders.size());

        var subassemblyMO = allOrders.stream()
                .filter(order -> order.product() != null && subassemblyId.equals(order.product().id().toString()))
                .findFirst()
                .orElse(null);
        assertNotNull("Manufacturing order should be automatically created for subassembly", subassemblyMO);
        assertEquals(productMOId, subassemblyMO.parent().id().toString());
        assertEquals(1, subassemblyMO.manufacturingTasks().size());

        var productMO = account.manufacturing().order(productMOId);
        assertEquals(subassemblyId, productMO.manufacturingOperations().getFirst().materials().get(1).materialGoods().getFirst().id().toString());
        assertFalse(productMO.manufacturingOperations().getFirst().materials().get(1).allAvailable());

        var subassemblyMOId = subassemblyMO.id().toString();
        account.manufacturing().completeTask(subassemblyMOId);
        account.manufacturing().recordConsumption(subassemblyMOId, List.of(Tuple.of(UUID.fromString(rawMaterialId1), BigDecimal.ONE)));
        inventoryForGoodEquals(account, productId, defaultInventoryUnitId, 0);
        inventoryForGoodEquals(account, subassemblyId, defaultInventoryUnitId, 1);
        inventoryForGoodEquals(account, rawMaterialId1, defaultInventoryUnitId, 9);

        productMO = account.manufacturing().order(productMOId);
        assertEquals(subassemblyId, productMO.manufacturingOperations().getFirst().materials().get(1).materialGoods().getFirst().id().toString());
        assertTrue(productMO.manufacturingOperations().getFirst().materials().get(1).allAvailable());

        account.manufacturing().completeTask(productMOId);
        account.manufacturing().recordConsumption(productMOId, List.of(
                Tuple.of(UUID.fromString(rawMaterialId1), BigDecimal.ONE),
                Tuple.of(UUID.fromString(subassemblyId), BigDecimal.ONE)
        ));

        inventoryForGoodEquals(account, productId, defaultInventoryUnitId, 1);
        inventoryForGoodEquals(account, subassemblyId, defaultInventoryUnitId, 0);
        inventoryForGoodEquals(account, rawMaterialId1, defaultInventoryUnitId, 8);
    }

    @Test
    public void subassemblyMONumberBasedOnParent() {
        TestData testData = createAccountWithSubassembly();

        var productMOId = testData.account.manufacturing().orderManufacturing(testData.productId, BigDecimal.ONE).manufacturingOrderId;
        var allOrders = testData.account.manufacturing().list();

        var parentMO = testData.account.manufacturing().order(productMOId);
        var subassemblyMO = allOrders.stream()
                .filter(order -> order.parent() != null && order.parent().id().toString().equals(productMOId))
                .findFirst()
                .orElse(null);

        assertNotNull("Subassembly MO should be created", subassemblyMO);
        assertTrue("Subassembly MO number should be based on parent",
                subassemblyMO.number().startsWith(parentMO.number() + " / "));
    }

    @Test
    public void subassemblyMOCompletedReservesStockForParent() {
        TestData testData = createAccountWithSubassembly();

        var productMOId = testData.account.manufacturing().orderManufacturing(testData.productId, BigDecimal.ONE).manufacturingOrderId;
        var subassemblyMO = testData.account.manufacturing().getChildOrders(productMOId).get(0);

        // Check that parent MO now has materials available
        var parentMO = testData.account.manufacturing().order(productMOId);
        var subassemblyMaterial = parentMO.manufacturingOperations().getFirst().materials().stream()
                .filter(m -> m.materialGoods().getFirst().id().toString().equals(testData.subassemblyId))
                .findFirst()
                .orElse(null);

        assertNotNull("Subassembly material should exist", subassemblyMaterial);
        assertFalse("Subassembly material should be available after completion", subassemblyMaterial.allAvailable());

        // Complete subassembly MO
        testData.account.manufacturing().completeTask(subassemblyMO.id().toString());
        testData.account.manufacturing().recordConsumption(subassemblyMO.id().toString(),
                List.of(Tuple.of(UUID.fromString(testData.rawMaterialId1), BigDecimal.ONE)));

        // Check that parent MO now has materials available
        parentMO = testData.account.manufacturing().order(productMOId);
        subassemblyMaterial = parentMO.manufacturingOperations().getFirst().materials().stream()
                .filter(m -> m.materialGoods().getFirst().id().toString().equals(testData.subassemblyId))
                .findFirst()
                .orElse(null);

        assertNotNull("Subassembly material should exist", subassemblyMaterial);
        assertTrue("Subassembly material should be available after completion", subassemblyMaterial.allAvailable());
    }

    @Test
    public void rankingChangeReReservesStockAndDeletesUnnecessarySubassemblyMOs() {
        TestData testData = createAccountWithSubassembly();

        // Create two product MOs
        var productMO1Id = testData.account.manufacturing().orderManufacturing(testData.productId, BigDecimal.ONE).manufacturingOrderId;
        var productMO2Id = testData.account.manufacturing().orderManufacturing(testData.productId, BigDecimal.ONE).manufacturingOrderId;

        assertEquals(1, testData.account.manufacturing().getChildOrders(productMO1Id).size());
        assertEquals(1, testData.account.manufacturing().getChildOrders(productMO2Id).size());

        // Add enough subassembly stock for one product
        testData.account.inventory().withGood(testData.subassemblyId).withQuantity(BigDecimal.ONE).fill();
        assertEquals(0, testData.account.manufacturing().getChildOrders(productMO1Id).size());
        assertEquals(1, testData.account.manufacturing().getChildOrders(productMO2Id).size());

        // Change ranking - put second order first
        testData.account.manufacturing().applyRanking(List.of(UUID.fromString(productMO2Id), UUID.fromString(productMO1Id)));
        assertEquals(1, testData.account.manufacturing().getChildOrders(productMO1Id).size());
        assertEquals(0, testData.account.manufacturing().getChildOrders(productMO2Id).size());

    }

    @Test
    public void updateOrderReReservesStockAndDeletesUnnecessarySubassemblyMOs() {
        TestData testData = createAccountWithSubassembly();

        var productMOId = testData.account.manufacturing().orderManufacturing(testData.productId, BigDecimal.valueOf(2)).manufacturingOrderId;
        var initialChildOrders = testData.account.manufacturing().getChildOrders(productMOId).size();
        assertEquals(1, initialChildOrders);

        testData.account.inventory().withGood(testData.subassemblyId).withQuantity(BigDecimal.valueOf(1)).fill();
        testData.account.manufacturing().updateOrder(productMOId, BigDecimal.ONE);

        var finalChildOrders = testData.account.manufacturing().getChildOrders(productMOId).size();
        assertEquals(0, finalChildOrders);
        var parentMO = testData.account.manufacturing().order(productMOId);
        var subassemblyMaterial = parentMO.manufacturingOperations().getFirst().materials().stream()
                .filter(m -> m.materialGoods().getFirst().id().toString().equals(testData.subassemblyId))
                .findFirst()
                .orElse(null);

        assertNotNull("Subassembly material should exist", subassemblyMaterial);
        assertTrue("Subassembly material should be available after completion", subassemblyMaterial.allAvailable());
    }

    @Test
    public void deleteOrderDeletesSubassemblyMOs() {
        TestData testData = createAccountWithSubassembly();
        var productMOId = testData.account.manufacturing().orderManufacturing(testData.productId, BigDecimal.ONE).manufacturingOrderId;
        var childOrders = testData.account.manufacturing().getChildOrders(productMOId);
        assertFalse("Should have child orders initially", childOrders.isEmpty());
        testData.account.manufacturing().deleteOrder(productMOId);
        childOrders.forEach(childOrder -> {
            assertFalse("Child order should be deleted",
                    testData.account.manufacturing().orderExists(childOrder.id().toString()));
        });
    }

    private TestData createAccountWithSubassembly() {
        Account account = setup
                .account()
                .category().withName("category for product and subassemblies").create().account()
                .location().create().account()
                .supplier().create().account();

        // Create raw materials
        var rawMaterialId1 = account.good()
                .withName("material_1")
                .withCategory(account.category().categoryId)
                .create().goodId;
        account.inventory().withGood(rawMaterialId1).withQuantity(BigDecimal.TEN).fill();

        var rawMaterialId2 = account.good()
                .withName("material_2")
                .withCategory(account.category().categoryId)
                .create().goodId;
        account.inventory().withGood(rawMaterialId2).withQuantity(BigDecimal.TEN).fill();

        // Create subassembly
        var subassemblyId = account.good()
                .withName("subassembly").withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(
                        List.of(UUID.fromString(rawMaterialId1), UUID.fromString(rawMaterialId2)),
                        BigDecimal.ONE, false, true, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        // Create product that uses subassembly
        var productId = account.good()
                .withName("product").withCategory(account.category().categoryId)
                .withMaterials(List.of(
                        new RequiredMaterial(List.of(UUID.fromString(rawMaterialId1)), BigDecimal.ONE, false, false, false, BigDecimal.ZERO, null, null),
                        new RequiredMaterial(List.of(UUID.fromString(subassemblyId)), BigDecimal.ONE, false, false, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        return new TestData(account, productId, rawMaterialId1, rawMaterialId2, subassemblyId);
    }

    private static class TestData {
        final Account account;
        final String productId;
        final String rawMaterialId1;
        final String rawMaterialId2;
        final String subassemblyId;

        TestData(Account account, String productId, String rawMaterialId1, String rawMaterialId2, String subassemblyId) {
            this.account = account;
            this.productId = productId;
            this.rawMaterialId1 = rawMaterialId1;
            this.rawMaterialId2 = rawMaterialId2;
            this.subassemblyId = subassemblyId;
        }
    }

    private void inventoryForGoodEquals(Account account, String materialGoodId, UUID inventoryUnitId, int quantity) {
        var stock = account.inventory().stockInformationFor(materialGoodId, inventoryUnitId);
        assertEquals(0, stock.quantity().compareTo(BigDecimal.valueOf(quantity)));
    }

}
