package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.http.controllers.manufacturing.ManufacturingOrdersController;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.closeTo;
import static org.hamcrest.Matchers.nullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class ManufacturingOrdersTests extends IntegrationTest {

    @Test
    public void manufacturingLifecycle() throws Exception {

        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .createRawMaterial().goodId;

        //create the product
        account.good()
                .withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(account.good().goodId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null)))
                .create();

        account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.TEN);

        mvc.perform(get("/manufacturing/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].id", is(account.manufacturing().manufacturingOrderId)))
                .andExpect(jsonPath("[0].status", is("SUBMITTED")))
                .andExpect(jsonPath("[0].product.id", is(account.good().goodId)))
                .andExpect(jsonPath("[0].manufacturedProducts", is(nullValue())))
                .andExpect(jsonPath("[0].manufacturingOperations[0].materials.[0].requiredTotal", closeTo(100, 0.001)))
                .andExpect(jsonPath("[0].manufacturingOperations[0].materials.[0].reservedTotal", is(0)));

        //add raw materials to inventory
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.TEN).fill();

        //update quantity
        mvc.perform(post("/manufacturing/orders/" + account.manufacturing().manufacturingOrderId + "/update")
                        .content(Json.write(new ManufacturingOrdersController.UpdateManufacturingOrder(null,
                                null,
                                BigDecimal.valueOf(5),
                                null, null, null)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        mvc.perform(get("/manufacturing/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].id", is(account.manufacturing().manufacturingOrderId)))
                .andExpect(jsonPath("[0].status", is("SUBMITTED")))
                .andExpect(jsonPath("[0].product.id", is(account.good().goodId)))
                .andExpect(jsonPath("[0].manufacturedProducts", is(nullValue())))
                .andExpect(jsonPath("[0].manufacturingOperations[0].materials.[0].requiredTotal", closeTo(50, 0.001)))
                .andExpect(jsonPath("[0].manufacturingOperations[0].materials.[0].reservedTotal", closeTo(10, 0.001)));

        //complete the first task
        account.manufacturing().completeTask();
        mvc.perform(get("/manufacturing/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].id", is(account.manufacturing().manufacturingOrderId)))
                .andExpect(jsonPath("[0].status", is("MANUFACTURED")))
                .andExpect(jsonPath("[0].manufacturedProducts.[0].quantity", closeTo(5, 0.01)));

        //finish the order
        account.manufacturing().recordConsumption(List.of(Tuple.of(UUID.fromString(rawMaterialId), BigDecimal.TEN)));
        mvc.perform(get("/manufacturing/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].id", is(account.manufacturing().manufacturingOrderId)))
                .andExpect(jsonPath("[0].status", is("CONSUMPTION_RECORDED")))
                .andExpect(jsonPath("[0].materialIssueNote.number", is("BC-0001")))
                .andExpect(jsonPath("[0].manufacturedProducts.[0].quantity", closeTo(5, 0.01)));

        //verify inventory
        mvc.perform(get("/inventory/list?ids=" + rawMaterialId)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].materialGoodId", is(rawMaterialId)))
                .andExpect(jsonPath("[0].quantity", closeTo(0.0, 0.001)));

        mvc.perform(get("/inventory/list?ids=" + account.good().goodId)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].materialGoodId", is(account.good().goodId)))
                .andExpect(jsonPath("[0].quantity", closeTo(5, 0.001)));
    }

    @Test
    public void rankingSupport() throws Exception {

        Account account = setup
                .account()
                .category().withName("Office Supplies").create().account()
                .supplier().withName("IKEA").create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        account.inventory().withGood(rawMaterialId).fill();

        var rossId = account.employee().withName("Ross Geller").create().employeeId;
        //create the product
        var paintStep = new ManufacturingOperation(null, List.of(), List.of(rossId),
                "paint", 20, false, null,null, null, null,
                List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null)));
        account.good()
                .withCategory(account.category().categoryId)
                .withOperations(List.of(paintStep))
                .create();

        //add raw materials to inventory
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.valueOf(100)).fill();

        var firstOrder = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.TEN).manufacturingOrderId;
        var secondOrder = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.TEN).manufacturingOrderId;

        mvc.perform(get("/manufacturing/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].id", is(firstOrder)))
                .andExpect(jsonPath("[0].status", is("SUBMITTED")))
                .andExpect(jsonPath("[0].ranking", is(0)))
                .andExpect(jsonPath("[1].id", is(secondOrder)))
                .andExpect(jsonPath("[1].status", is("SUBMITTED")))
                .andExpect(jsonPath("[1].ranking", is(1)));

        mvc.perform(post("/manufacturing/orders/ranking")
                        .header("Authorization", "Bearer " + account.accountId)
                        .content(Json.write(List.of(secondOrder, firstOrder)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mvc.perform(get("/manufacturing/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].id", is(secondOrder)))
                .andExpect(jsonPath("[0].ranking", is(0)))
                .andExpect(jsonPath("[1].id", is(firstOrder)))
                .andExpect(jsonPath("[1].ranking", is(1)));
    }

}
