package fabriqon.app.business.sales;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public record SalesOrder
        (
                UUID id,
                Instant createTime,
                Instant updateTime,
                boolean deleted,

                UUID ownerId,
                UUID customerId,
                String number,
                Integer ranking,
                LocalDateTime deliveryDeadline,
                Status status,
                List<Item> items,
                Address shippingAddress,
                String notes,
                String customerNotes,
                LocalDate offerDate,
                LocalDate offerExpiration,
                RenderingDetails renderingDetails
        ) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "customer_id", "number", "ranking",
            "delivery_deadline", "status", "items", "shippingAddress", "notes", "customer_notes", "offer_date", "offer_expiration", "rendering_details"})
    public SalesOrder(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, UUID customerId,
                      String number, Integer ranking, LocalDateTime deliveryDeadline, Status status, String items,
                      String shippingAddress, String notes, String customerNotes, LocalDate offerDate, LocalDate offerExpiration, String renderingDetails) {
        this(id, createTime, updateTime, deleted, ownerId, customerId, number, ranking,  deliveryDeadline, status,
                Json.read(items, new TypeReference<List<Item>>() {}),
                Json.read(shippingAddress, Address.class),
                notes, customerNotes,
                offerDate, offerExpiration,
                Json.readNullSafe(renderingDetails, RenderingDetails.class)
        );
    }

    public static final int UNRANKED_ORDER_VALUE = -1;

    public SalesOrder setRenderingDetails(RenderingDetails renderingDetails) {
        return new SalesOrder(id, createTime, updateTime, deleted, ownerId, customerId, number, ranking, deliveryDeadline, status, items, shippingAddress, notes, customerNotes, offerDate, offerExpiration, renderingDetails);
    }

    public SalesOrder setCustomerNotes(String customerNotes) {
        return new SalesOrder(id, createTime, updateTime, deleted, ownerId, customerId, number, ranking, deliveryDeadline, status, items, shippingAddress, notes, customerNotes, offerDate, offerExpiration, renderingDetails);
    }

    public enum Status {
        IN_QUOTATION,
        QUOTE_SENT,
        QUOTE_CANCELED,
        CANCELED,
        SUBMITTED,
        PROCESSING,
        PICKING_PACKING,
        READY_TO_SHIP,
        SHIPPING,
        DELIVERED,
        ON_HOLD
    }

    /**
     * If product id is specified it represents a product otherwise a service
     */
    public record Item(
            UUID productId, UUID serviceId, BigDecimal quantity, Money price, BigDecimal discount, String customizationNote,
            List<Customization> customizations, Boolean addedToWishlist
    ) {
        public record Customization(
                UUID materialId,
                BigDecimal quantity
        ) {}

        public Item addedToWishlist(Boolean addedToWishlist) {
            return new Item(productId, serviceId, quantity, price, discount, customizationNote,
                    customizations, addedToWishlist
            );
        }
    }

    public record RenderingDetails(
            String language,
            List<HideableColumn> columnsToHide
    ) {
        public enum HideableColumn { DISCOUNT }
    }
}
