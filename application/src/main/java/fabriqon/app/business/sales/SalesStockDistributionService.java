package fabriqon.app.business.sales;

import fabriqon.app.business.inventory.InventoryService;
import fabriqon.misc.MathUtils;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

import static fabriqon.jooq.JooqJsonbFunctions.arrayContainsJson;
import static fabriqon.jooq.classes.Tables.RESERVED_INVENTORY;
import static fabriqon.jooq.classes.Tables.SALES_ORDER;
import static java.lang.String.format;
import static org.jooq.impl.DSL.coalesce;
import static org.jooq.impl.DSL.sum;

/**
 * Centralized service for distributing stock to sales orders.
 * Consolidates logic from SalesInventoryChangedConsumer and ProductsManufacturedConsumer.
 */
@Service
public class SalesStockDistributionService {

    private final DSLContext db;
    private final InventoryService inventory;
    private final SalesService salesService;

    @Autowired
    public SalesStockDistributionService(DSLContext db, InventoryService inventory, SalesService salesService) {
        this.db = db;
        this.inventory = inventory;
        this.salesService = salesService;
    }

    /**
     * Distributes stock to sales orders based on ranking priority.
     * Used for general inventory changes.
     */
    public void distributeStockToOrders(UUID ownerId, UUID productId, BigDecimal quantity) {
        AtomicReference<BigDecimal> available = new AtomicReference<>(quantity);
        Queue<SalesOrder> orders = getEligibleSalesOrders(ownerId, productId);

        while (available.get().compareTo(BigDecimal.ZERO) > 0 && !orders.isEmpty()) {
            var order = orders.remove();
            var reservedQuantity = getReservedQuantity(order, productId);
            var requiredQuantity = getRequiredQuantity(productId, order);
            
            if (requiredQuantity.compareTo(reservedQuantity) > 0) {
                var newlyReserved = MathUtils.min(available.get(), requiredQuantity.subtract(reservedQuantity));
                inventory.reserveStockForSalesOrder(order.ownerId(), order.id(),
                        List.of(new InventoryService.Stock(productId, newlyReserved)));
                available.set(available.get().subtract(newlyReserved));
                
                checkAndUpdateOrderStatus(order.ownerId(), order.id());
            }
        }
    }

    /**
     * Handles specific sales order allocation with complex partial fulfillment logic.
     * Used when products are manufactured for a specific sales order.
     */
    public void allocateToSpecificSalesOrder(UUID ownerId, UUID salesOrderId, UUID productId, 
                                           BigDecimal quantity, boolean customProduct, UUID fromOrderId) {
        var order = getSalesOrder(salesOrderId);
        var requiredQuantity = getRequiredQuantity(productId, order);
        var currentlyReserved = getReservedQuantity(order, productId);

        if (requiredQuantity.compareTo(quantity) == 0) {
            // Exact match - clear existing reservations and reserve new stock
            clearExistingReservations(ownerId, salesOrderId, productId);
            inventory.reserveStockForSalesOrder(ownerId, salesOrderId,
                    List.of(new InventoryService.Stock(productId, quantity)),
                    customProduct, fromOrderId);
            
            if (currentlyReserved.compareTo(BigDecimal.ZERO) > 0) {
                // Redistribute what was previously reserved
                distributeStockToOrders(ownerId, productId, currentlyReserved);
            }
        } else {
            // Partial fulfillment logic
            if (currentlyReserved.add(quantity).compareTo(requiredQuantity) >= 0) {
                // We have enough to fulfill the order completely
                var remainder = currentlyReserved.add(quantity).subtract(requiredQuantity);
                inventory.reserveStockForSalesOrder(ownerId, salesOrderId,
                        List.of(new InventoryService.Stock(productId, quantity.subtract(remainder))),
                        customProduct, fromOrderId);
                
                if (remainder.compareTo(BigDecimal.ZERO) > 0) {
                    // Redistribute the remainder
                    distributeStockToOrders(ownerId, productId, remainder);
                }
            } else {
                // Reserve all available quantity
                inventory.reserveStockForSalesOrder(ownerId, salesOrderId,
                        List.of(new InventoryService.Stock(productId, quantity)),
                        customProduct, fromOrderId);
            }
        }

        checkAndUpdateOrderStatus(ownerId, salesOrderId);
    }

    /**
     * Removes reserved stock from sales orders when inventory decreases.
     */
    public void removeReservedStock(UUID ownerId, UUID productId, BigDecimal toRemove) {
        Queue<SalesOrder> orders = getEligibleSalesOrdersForRemoval(ownerId, productId);

        while (toRemove.compareTo(BigDecimal.ZERO) < 0 && !orders.isEmpty()) {
            var order = orders.remove();
            var reservedQuantity = getReservedQuantity(order, productId);
            
            if (reservedQuantity.compareTo(BigDecimal.ZERO) > 0) {
                var toRemoveReserved = MathUtils.min(toRemove.abs(), reservedQuantity);
                inventory.clearReserved(order.id(), null, order.ownerId(),
                        new InventoryService.Stock(productId, toRemoveReserved));
                toRemove = toRemove.add(toRemoveReserved);
                
                // Reset status if order was in advanced stages
                if (order.status() == SalesOrder.Status.READY_TO_SHIP || 
                    order.status() == SalesOrder.Status.PICKING_PACKING) {
                    db.update(SALES_ORDER)
                            .set(SALES_ORDER.STATUS, SalesOrder.Status.PROCESSING.name())
                            .where(SALES_ORDER.ID.eq(order.id()))
                            .execute();
                }
            }
        }
    }

    private Queue<SalesOrder> getEligibleSalesOrders(UUID ownerId, UUID productId) {
        return new LinkedList<>(db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.DELETED.isFalse()
                        .and(SALES_ORDER.OWNER_ID.eq(ownerId))
                        .and(SALES_ORDER.STATUS.in(SalesOrder.Status.SUBMITTED.name(), SalesOrder.Status.PROCESSING.name()))
                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", productId))))
                .orderBy(SALES_ORDER.RANKING.asc())
                .fetchInto(SalesOrder.class));
    }

    private Queue<SalesOrder> getEligibleSalesOrdersForRemoval(UUID ownerId, UUID productId) {
        return new LinkedList<>(db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.DELETED.isFalse()
                        .and(SALES_ORDER.OWNER_ID.eq(ownerId))
                        .and(SALES_ORDER.STATUS.in(SalesOrder.Status.SUBMITTED.name(), 
                                                  SalesOrder.Status.PROCESSING.name(), 
                                                  SalesOrder.Status.PICKING_PACKING.name(), 
                                                  SalesOrder.Status.READY_TO_SHIP.name()))
                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", productId))))
                .orderBy(SALES_ORDER.RANKING.desc())
                .fetchInto(SalesOrder.class));
    }

    private SalesOrder getSalesOrder(UUID salesOrderId) {
        return db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(SalesOrder.class);
    }

    private BigDecimal getReservedQuantity(SalesOrder order, UUID materialGoodId) {
        return db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                .from(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(order.ownerId()),
                        RESERVED_INVENTORY.SALES_ORDER_ID.eq(order.id()),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(materialGoodId))
                .fetchOptionalInto(BigDecimal.class).orElse(BigDecimal.ZERO);
    }

    private BigDecimal getRequiredQuantity(UUID productId, SalesOrder order) {
        return order.items().stream()
                .filter(product -> productId.equals(product.productId()))
                .map(SalesOrder.Item::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void clearExistingReservations(UUID ownerId, UUID salesOrderId, UUID productId) {
        db.deleteFrom(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(productId),
                        RESERVED_INVENTORY.SALES_ORDER_ID.eq(salesOrderId),
                        RESERVED_INVENTORY.OWNER_ID.eq(ownerId))
                .execute();
    }

    private void checkAndUpdateOrderStatus(UUID ownerId, UUID orderId) {
        if (salesService.allItemsAvailable(ownerId, orderId)) {
            salesService.setPickAndPackStatus(ownerId, orderId);
        }
    }
}
