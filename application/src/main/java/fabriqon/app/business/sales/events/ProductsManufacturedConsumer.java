package fabriqon.app.business.sales.events;

import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.events.ProductsManufactured;
import fabriqon.app.business.sales.SalesStockDistributionService;
import fabriqon.events.EventConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ProductsManufacturedConsumer extends EventConsumer<ProductsManufactured> {

    private final DSLContext db;
    private final InventoryService inventory;
    private final SalesService salesService;

    @Autowired
    public ProductsManufacturedConsumer(DSLContext db, InventoryService inventory, SalesService salesService) {
        this.db = db;
        this.inventory = inventory;
        this.salesService = salesService;
    }

    //    @Transactional //TODO: resolve issues with persistentClass (uncomment and run tests)
    @Override
    public void process(ProductsManufactured event) {
        if (event.data.parentId() != null) {//associated to a parent order
            inventory.reserveStockForManufacturingOrder(event.data.ownerId(), event.data.parentId(), List.of(new InventoryService.Stock(event.data.productId(), event.data.quantity())), true, event.data.orderId());
        } else if (event.data.salesOrderId() != null) {//associated to a sales order
            //we reserve the products for the associated sale order
            var order = salesOrder(event.data.salesOrderId());
            var requiredQuantity = requiredQuantity(event.data.productId(), order);
            var currentlyReserved = reservedQuantity(order, event.data.productId());
            if (requiredQuantity.compareTo(event.data.quantity()) == 0) {
                //we might have something reserved so we clear it
                db.deleteFrom(RESERVED_INVENTORY)
                                .where(RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(event.data.productId()),
                                        RESERVED_INVENTORY.SALES_ORDER_ID.eq(order.id()),
                                        RESERVED_INVENTORY.OWNER_ID.eq(event.data.ownerId()))
                                        .execute();
                inventory.reserveStockForSalesOrder(event.data.ownerId(), event.data.salesOrderId(),
                        List.of(new InventoryService.Stock(event.data.productId(), event.data.quantity())),
                        event.data.customProduct(), event.data.orderId()
                );
                if (currentlyReserved.compareTo(BigDecimal.ZERO) > 0) {//redistribute what was left
                    distributeStockForOrders(event.data.ownerId(), event.data.productId(), currentlyReserved);
                }
            } else {
                if (currentlyReserved.add(event.data.quantity()).compareTo(requiredQuantity) >= 0) {//we will have some left over
                    var remainder = currentlyReserved.add(event.data.quantity()).subtract(requiredQuantity);
                    inventory.reserveStockForSalesOrder(event.data.ownerId(), event.data.salesOrderId(),
                            List.of(new InventoryService.Stock(event.data.productId(), event.data.quantity().subtract(remainder))),
                            event.data.customProduct(), event.data.orderId()
                    );
                    if (remainder.compareTo(BigDecimal.ZERO) > 0) {//redistribute what was left
                        distributeStockForOrders(event.data.ownerId(), event.data.productId(), remainder);
                    }
                } else {
                    inventory.reserveStockForSalesOrder(event.data.ownerId(), event.data.salesOrderId(),
                            List.of(new InventoryService.Stock(event.data.productId(), event.data.quantity())),
                            event.data.customProduct(), event.data.orderId()
                    );
                }
            }
            if (salesService.allItemsAvailable(event.data.ownerId(), event.data.salesOrderId())) {
                salesService.pickAndPackOrder(event.data.ownerId(), event.data.salesOrderId());
            }
        } else {
            //we reserve the products for any other SO
            distributeStockForOrders(event.data.ownerId(), event.data.productId(),event.data.quantity());
        }
    }

    private void distributeStockForOrders(UUID ownerId, UUID productId, BigDecimal quantity) {
        AtomicReference<BigDecimal> available = new AtomicReference<>(quantity);
        //get orders that need the product in ranking order
        Queue<SalesOrder> orders = new LinkedList<>(db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.DELETED.isFalse()
                        .and(SALES_ORDER.OWNER_ID.eq(ownerId))
                        .and(SALES_ORDER.STATUS.in(SalesOrder.Status.SUBMITTED.name(), SalesOrder.Status.PROCESSING.name()))
                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", productId)))
                )
                .orderBy(SALES_ORDER.RANKING.asc())
                .fetchInto(SalesOrder.class));

        while (available.get().compareTo(BigDecimal.ZERO) > 0 && !orders.isEmpty()) {
            var order = orders.remove();
            var reservedQuantity = reservedQuantity(order, productId);
            var requiredQuantity = requiredQuantity(productId, order);
            if (requiredQuantity.compareTo(reservedQuantity) > 0) {
                //reserve what is available
                var newlyReserved = min(available.get(), requiredQuantity.subtract(reservedQuantity));
                inventory.reserveStockForSalesOrder(order.ownerId(), order.id(),
                        List.of(new InventoryService.Stock(productId, newlyReserved)));
                available.set(available.get().subtract(newlyReserved));
                if (salesService.allItemsAvailable(order.ownerId(), order.id())) {
                    salesService.setPickAndPackStatus(order.ownerId(), order.id());
                }
            }
        }
    }

    private SalesOrder salesOrder(UUID salesOrderId) {
        return db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(SalesOrder.class);
    }

    private BigDecimal reservedQuantity(SalesOrder order, UUID materialGoodId) {
        return db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                .from(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(order.ownerId()),
                        RESERVED_INVENTORY.SALES_ORDER_ID.eq(order.id()),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(materialGoodId)
                )
                .fetchOptionalInto(BigDecimal.class).orElse(BigDecimal.ZERO);
    }

    private BigDecimal requiredQuantity(UUID productId, SalesOrder order) {
        return order.items().stream()
                .filter(product -> productId.equals(product.productId()))
                .map(SalesOrder.Item::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
