package fabriqon.app.business.sales;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.Notification;
import fabriqon.ObjectStorage;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.customers.Note;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.invoicing.Invoice;
import fabriqon.app.business.invoicing.InvoiceService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.manufacturing.ManufacturingService;
import fabriqon.app.business.notifications.UserTaggingProcessor;
import fabriqon.app.business.purchases.PurchaseWishlistItem;
import fabriqon.app.business.purchases.PurchasesService;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.app.business.services.ServicingService;
import fabriqon.app.business.systemevents.SystemEvent;
import fabriqon.app.business.systemevents.SystemEventGenerator;
import fabriqon.app.business.systemevents.SystemEventTargetEntityId;
import fabriqon.app.common.model.*;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static fabriqon.app.business.sales.SalesOrder.Status.*;
import static fabriqon.app.business.sales.SalesOrder.UNRANKED_ORDER_VALUE;
import static fabriqon.jooq.classes.Tables.*;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.*;

@Component
@Transactional
public class SalesService {

    private final Sequences sequences;
    private final DSLContext db;
    private final InventoryService inventory;
    private final ManufacturingService manufacturing;
    private final ServicingService servicing;
    private final InvoiceService invoicing;
    private final Templates templates;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final ObjectStorage objectStorage;
    private final GoodsAccompanyingNotesService goodsAccompanyingNotesService;
    private final VatCalculatorProvider vatCalculatorProvider;
    private final PurchasesService purchasesService;
    private final UserTaggingProcessor userTaggingProcessor;


    @Autowired
    public SalesService(Sequences sequences, DSLContext db, InventoryService inventory,
                        ManufacturingService manufacturing, ServicingService servicing, InvoiceService invoicing,
                        Templates templates, HtmlToPdfConverter htmlToPdfConverter, ObjectStorage objectStorage,
                        GoodsAccompanyingNotesService goodsAccompanyingNotesService, VatCalculatorProvider vatCalculatorProvider,
                        PurchasesService purchasesService, UserTaggingProcessor userTaggingProcessor) {
        this.sequences = sequences;
        this.db = db;
        this.inventory = inventory;
        this.manufacturing = manufacturing;
        this.servicing = servicing;
        this.invoicing = invoicing;
        this.templates = templates;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.objectStorage = objectStorage;
        this.goodsAccompanyingNotesService = goodsAccompanyingNotesService;
        this.vatCalculatorProvider = vatCalculatorProvider;
        this.purchasesService = purchasesService;
        this.userTaggingProcessor = userTaggingProcessor;
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES)
    public SalesOrder createSalesOrder(UUID ownerId, UUID customerId, LocalDateTime createTime,
                                       LocalDateTime deliveryDeadline, SalesOrder.Status status,
                                       List<SalesOrder.Item> items, Address shippingAddress,
                                       String notes, String customerNotes,
                                       LocalDate offerDate, LocalDate offerExpiration,
                                       SalesOrder.RenderingDetails renderingDetails
    ) {
        var orderId = UUID.randomUUID();
        var ranking = getRanking(ownerId);
        final SalesOrder order = new SalesOrder(
                orderId, Instant.now(), Instant.now(), false,
                ownerId, customerId, sequences.nextSequenceForSalesOrder(ownerId),
                status != IN_QUOTATION ? ++ranking : null,
                deliveryDeadline,
                status == null ? SUBMITTED : status,
                items,
                shippingAddress,
                notes,
                customerNotes,
                offerDate != null && status == IN_QUOTATION ? LocalDate.now() : offerDate, offerExpiration,
                renderingDetails
        );

        db.insertInto(SALES_ORDER)
                .set(SALES_ORDER.ID, order.id())
                .set(SALES_ORDER.OWNER_ID, order.ownerId())
                .set(SALES_ORDER.CUSTOMER_ID, order.customerId())
                .set(SALES_ORDER.NUMBER, order.number())
                .set(SALES_ORDER.RANKING, order.ranking())
                .set(SALES_ORDER.CREATE_TIME, createTime)
                .set(SALES_ORDER.DELIVERY_DEADLINE, order.deliveryDeadline())
                .set(SALES_ORDER.STATUS, order.status().name())
                .set(SALES_ORDER.ITEMS, JSONB.valueOf(Json.write(order.items())))
                .set(SALES_ORDER.SHIPPING_ADDRESS, JSONB.valueOf(Json.write(order.shippingAddress())))
                .set(SALES_ORDER.OFFER_DATE, order.offerDate() != null ? order.offerDate().atStartOfDay() : null)
                .set(SALES_ORDER.OFFER_EXPIRATION, order.offerExpiration() != null ? order.offerExpiration().atStartOfDay() : null)
                .set(SALES_ORDER.RENDERING_DETAILS, order.renderingDetails() != null ? JSONB.valueOf(Json.write(order.renderingDetails())) : null)
                .execute();

        if (order.status() == SUBMITTED) {
            reserveStock(ownerId, items, orderId);
            if (allItemsAvailable(ownerId, orderId) && items.stream().allMatch(item -> item.productId() != null)) {
                db.update(SALES_ORDER)
                        .set(SALES_ORDER.STATUS, PICKING_PACKING.name())
                        .where(SALES_ORDER.ID.eq(orderId))
                        .execute();
            }
        }
        return order;
    }

    public SalesOrder updateSalesOrder(UUID salesOrderId, UUID ownerId, SalesOrder update) {
        var ownershipCriteria = SALES_ORDER.ID.eq(salesOrderId).and(SALES_ORDER.OWNER_ID.eq(ownerId));

        var status = db.select(SALES_ORDER.STATUS).from(SALES_ORDER).where(ownershipCriteria).fetchSingleInto(SalesOrder.Status.class);
        if (List.of(SalesOrder.Status.CANCELED, SalesOrder.Status.DELIVERED, SalesOrder.Status.SHIPPING).contains(status)) {
            throw new BusinessException("Order cannot be modified in current status" + status, "incorrect_status", status);
        }

        var updateStatement = db.update(SALES_ORDER)
                .set(SALES_ORDER.OWNER_ID, ownerId);
        if (update.customerId() != null) {
            updateStatement = updateStatement.set(SALES_ORDER.CUSTOMER_ID, update.customerId());
        }
        if (update.deliveryDeadline() != null) {
            updateStatement = updateStatement.set(SALES_ORDER.DELIVERY_DEADLINE, update.deliveryDeadline());
        }
        if (update.offerDate() != null) {
            updateStatement = updateStatement.set(SALES_ORDER.OFFER_DATE, update.offerDate().atStartOfDay());
        }
        if (update.offerExpiration() != null) {
            updateStatement = updateStatement.set(SALES_ORDER.OFFER_EXPIRATION, update.offerExpiration().atStartOfDay());
        }
        if (update.renderingDetails() != null) {
            updateStatement = updateStatement.set(SALES_ORDER.RENDERING_DETAILS, JSONB.valueOf(Json.write(update.renderingDetails())));
        }
        Set<Tuple.Tuple2<UUID, BigDecimal>> currentItemsQuantities = null;
        if (update.items() != null) {
            var currentItems = Json.read(db.select(SALES_ORDER.ITEMS).from(SALES_ORDER).where(ownershipCriteria).fetchSingleInto(String.class),
                    new TypeReference<List<SalesOrder.Item>>() {});
            currentItemsQuantities = currentItems.stream()
                    .filter(i -> i.productId() != null)
                    .map(i -> Tuple.of(i.productId(), i.quantity()))
                    .collect(Collectors.toSet());

            //check if there is an update for the quantities of configurable or custom products as those cannot be modified if they have an MO attached
            var finalCurrentItemsQuantities = currentItemsQuantities;
            if (update.items().stream()
                    .filter(item -> item.productId() != null)
                    .filter(item -> finalCurrentItemsQuantities.stream().anyMatch(i -> item.productId().equals(i.a()) && item.quantity().compareTo(i.b()) != 0))
                    .filter(item -> isNotBlank(item.customizationNote()) || productDefinition(item.productId()).details.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).anyMatch(m -> m.optional() || m.configurableWithOptions()))
                    .anyMatch(item -> db.fetchCount(MANUFACTURING_ORDER, MANUFACTURING_ORDER.SALES_ORDER_ID.eq(salesOrderId), MANUFACTURING_ORDER.PRODUCT_ID.eq(item.productId())) == 1)) {
                throw new BusinessException("Can't edit quantity for configurable/custom product if it has an MO associated to it.", "quantity_edit_not_allowed_mo_exists");
            }
            //process the items that were removed
            currentItems.forEach(item -> {
                if (update.items().stream().noneMatch(i -> (
                        i.productId() != null && item.productId() != null && i.productId().equals(item.productId()))
                        || i.serviceId() != null && item.serviceId() != null && i.serviceId().equals(item.serviceId()))) {
                    //this item was removed so we delete the MO if there is one
                    db.select(MANUFACTURING_ORDER.ID, MANUFACTURING_ORDER.STATUS)
                            .from(MANUFACTURING_ORDER)
                            .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.SALES_ORDER_ID.eq(salesOrderId),
                                    item.productId() != null ? MANUFACTURING_ORDER.PRODUCT_ID.eq(item.productId()) : MANUFACTURING_ORDER.SERVICE_ID.eq(item.serviceId()))
                            .fetchOptional()
                            .ifPresent(r -> {
                                if (r.value2().equals(ManufacturingOrder.Status.MANUFACTURING.name())
                                        || r.value2().equals(ManufacturingOrder.Status.MANUFACTURED.name())
                                        || r.value2().equals(ManufacturingOrder.Status.CONSUMPTION_RECORDED.name())
                                ) {
                                    throw new BusinessException("Can't remove " + (item.productId() != null ? item.productId() : item.serviceId()) + " from the order as it has an in progress/done MO attached",
                                            "removed_item_has_in_progress_mo", (item.productId() != null ? productName(item.productId()) : serviceName(item.serviceId())));
                                } else {
                                    manufacturing.deleteOrder(ownerId, r.value1());
                                }
                            });
                }
            });

            updateStatement = updateStatement.set(SALES_ORDER.ITEMS, JSONB.valueOf(Json.write(
                    update.items().stream()
                            .map(item -> item.addedToWishlist(currentItems.stream()
                                    .filter(ci -> ci.productId() != null && ci.productId().equals(item.productId()))
                                    .findFirst().map(SalesOrder.Item::addedToWishlist).orElse(null)))
                            .toList()
            )));
        }
        if (update.shippingAddress() != null) {
            updateStatement = updateStatement.set(SALES_ORDER.SHIPPING_ADDRESS, JSONB.valueOf(Json.write(update.shippingAddress())));
        }
        if (update.notes() != null) {
            updateStatement = updateStatement.set(SALES_ORDER.NOTES, update.notes());
        }

        updateStatement
                .where(SALES_ORDER.ID.eq(salesOrderId), SALES_ORDER.OWNER_ID.eq(ownerId))
                .execute();

        if (isNotEmpty(update.items()) && (List.of(SUBMITTED, PROCESSING, PICKING_PACKING, READY_TO_SHIP).contains(status))) {
            invoicing.updateItems(ownerId, salesOrderId,
                    update.items().stream().map(item -> new Invoice.Item(
                            item.productId(),
                            item.serviceId(),
                            item.serviceId() != null ? serviceName(item.serviceId()) : productDefinition(item.productId()).name,
                            item.quantity(),
                            measurementUnit(item),
                            item.price(),
                            item.discount(),
                            null
                    )).toList());

            var newItems = update.items().stream()
                    .filter(i -> i.productId() != null)
                    .map(i -> Tuple.of(i.productId(), i.quantity()))
                    .collect(Collectors.toSet());
            if (!currentItemsQuantities.equals(newItems)) {
                //stock requirements have changed so we need to re-reserve and re-distribute stock by simulating a ranking change
                applyRanking(ownerId, db.select(SALES_ORDER.ID).from(SALES_ORDER)
                        .where(SALES_ORDER.OWNER_ID.eq(ownerId),
                                SALES_ORDER.RANKING.greaterOrEqual(select(SALES_ORDER.RANKING).from(SALES_ORDER).where(SALES_ORDER.ID.eq(salesOrderId))))
                        .orderBy(SALES_ORDER.RANKING)
                        .fetchInto(UUID.class),
                        false);
            }
        }

        return db.selectFrom(SALES_ORDER)
                .where(ownershipCriteria)
                .fetchSingleInto(SalesOrder.class);
    }

    /**
     * Method used to change the ranking/priority and re-distribute stock for the sales orders.
     * This can only be performed on orders that are still 'active' (i.e. not done).
     * During reordering the inventory is reserved again by the specified ranking
     */
    public void applyRanking(UUID ownerId, List<UUID> orderIds, boolean changeRanking) {
        if (db.fetchCount(SALES_ORDER,
                not(SALES_ORDER.STATUS.in(IN_QUOTATION.name(), SUBMITTED.name(), PROCESSING.name(), PICKING_PACKING.name(), READY_TO_SHIP.name())),
                SALES_ORDER.ID.in(orderIds)) > 0) {
            throw new BusinessException("You can only reorder ranking for orders that are: 'IN_QUOTATION', 'SUBMITTED', 'PROCESSING' or 'READY_TO_SHIP'", "invalid_so_order_statuses");
        }

        //first we drop all reserved inventory for the sales orders - excluding custom product reservations
        db.deleteFrom(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(ownerId), RESERVED_INVENTORY.SALES_ORDER_ID.in(orderIds), RESERVED_INVENTORY.MANDATORY_FOR_ORDER.isFalse())
                .execute();
        IntStream.range(0, orderIds.size()).forEach(
                index -> {
                    var order = order(orderIds.get(index));
                    //we re-reserve the inventory for each in their order
                    reserveStock(ownerId, order.items(), order.id());
                    var allItemsAvailable = allItemsAvailable(ownerId, order.id());
                    if (allItemsAvailable && List.of(SUBMITTED, PROCESSING).contains(order.status())) {
                        db.update(SALES_ORDER)
                                .set(SALES_ORDER.STATUS, PICKING_PACKING.name())
                                .where(SALES_ORDER.ID.eq(order.id()))
                                .execute();
                    } else if (!allItemsAvailable && (READY_TO_SHIP == order.status() || PICKING_PACKING == order.status())) {
                        db.update(SALES_ORDER)
                                .set(SALES_ORDER.STATUS, PROCESSING.name())
                                .where(SALES_ORDER.ID.eq(order.id()))
                                .execute();
                    }
                    if (changeRanking) {
                        //and update the ranking if needed
                        db.update(SALES_ORDER)
                                .set(SALES_ORDER.RANKING, index)
                                .where(SALES_ORDER.ID.eq(order.id()))
                                .execute();
                    }
                }
        );
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "QUOTE_CANCELED")
    public void cancelQuote(UUID accountId, @SystemEventTargetEntityId  UUID orderId) {
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, SalesOrder.Status.QUOTE_CANCELED.name())
                .set(SALES_ORDER.RANKING, -1)
                .where(SALES_ORDER.OWNER_ID.eq(accountId), SALES_ORDER.ID.eq(orderId),
                        SALES_ORDER.STATUS.in(IN_QUOTATION.name(), QUOTE_SENT.name())
                )
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "CANCELED")
    public void cancelOrder(UUID accountId, @SystemEventTargetEntityId  UUID orderId) {
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, SalesOrder.Status.CANCELED.name())
                .set(SALES_ORDER.RANKING, -1)
                .where(SALES_ORDER.OWNER_ID.eq(accountId), SALES_ORDER.ID.eq(orderId),
                        SALES_ORDER.STATUS.in(IN_QUOTATION.name(), QUOTE_SENT.name(), SUBMITTED.name(), READY_TO_SHIP.name())
                )
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
        inventory.clearReservedForSalesOrder(accountId, orderId);
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "ON_HOLD")
    public void onHoldOrder(UUID accountId, @SystemEventTargetEntityId  UUID orderId, UUID userId, String reason) {
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, SalesOrder.Status.ON_HOLD.name())
                .where(SALES_ORDER.OWNER_ID.eq(accountId), SALES_ORDER.ID.eq(orderId),
                        SALES_ORDER.STATUS.in(IN_QUOTATION.name(), QUOTE_SENT.name(), SUBMITTED.name(), READY_TO_SHIP.name())
                )
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
        addNote(accountId, orderId, userId, reason);
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "QUOTE_SENT")
    public void quoteSent(UUID ownerId, @SystemEventTargetEntityId  UUID orderId) {
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, QUOTE_SENT.name())
                .set(SALES_ORDER.RANKING, getRanking(ownerId) + 1)
                .where(SALES_ORDER.OWNER_ID.eq(ownerId), SALES_ORDER.ID.eq(orderId), SALES_ORDER.STATUS.eq(IN_QUOTATION.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "SUBMITTED")
    public void confirmOrder(UUID ownerId, @SystemEventTargetEntityId UUID orderId) {
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, SUBMITTED.name())
                .set(SALES_ORDER.RANKING, getRanking(ownerId) + 1)
                .where(SALES_ORDER.OWNER_ID.eq(ownerId), SALES_ORDER.ID.eq(orderId), SALES_ORDER.STATUS.in(IN_QUOTATION.name(),  QUOTE_SENT.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
        reserveStock(ownerId, order(orderId).items(), orderId);
        if (allItemsAvailable(ownerId, orderId)) {
            db.update(SALES_ORDER)
                    .set(SALES_ORDER.STATUS, PICKING_PACKING.name())
                    .where(SALES_ORDER.ID.eq(orderId))
                    .execute();
        }
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "PROCESSING")
    public void processingOrder(UUID ownerId, @SystemEventTargetEntityId UUID orderId) {
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, PROCESSING.name())
                .set(SALES_ORDER.RANKING, getRanking(ownerId) + 1)
                .where(SALES_ORDER.OWNER_ID.eq(ownerId), SALES_ORDER.ID.eq(orderId), SALES_ORDER.STATUS.in(SUBMITTED.name(), READY_TO_SHIP.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "PICKING_PACKING")
    public void pickAndPackOrder(UUID ownerId, @SystemEventTargetEntityId UUID orderId) {
        setPickAndPackStatus(ownerId, orderId);
    }

    /**
     * Used in internal flows not 'directly' triggered by users. We're doing this because there can be many flows
     * that trigger this automatically that we don't want to register as a system event
     */
    public void setPickAndPackStatus(UUID ownerId, UUID orderId) {
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, PICKING_PACKING.name())
                .where(SALES_ORDER.OWNER_ID.eq(ownerId), SALES_ORDER.ID.eq(orderId), SALES_ORDER.STATUS.in(SUBMITTED.name(), PROCESSING.name(), READY_TO_SHIP.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "READY_TO_SHIP")
    public void readyToShipOrder(UUID accountId, @SystemEventTargetEntityId UUID orderId) {
        if (notAllItemsReserved(orderId)) {
            throw new BusinessException("Not all items are reserved.", "not_all_reserved");
        }
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, READY_TO_SHIP.name())
                .where(SALES_ORDER.OWNER_ID.eq(accountId), SALES_ORDER.ID.eq(orderId),
                        SALES_ORDER.STATUS.in(IN_QUOTATION.name(), QUOTE_SENT.name(), SUBMITTED.name(), PROCESSING.name(), PICKING_PACKING.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "SHIPPING")
    public void shipOrder(UUID accountId, @SystemEventTargetEntityId UUID orderId) {
        if (notAllItemsReserved(orderId)) {
            throw new BusinessException("Not all items are reserved.", "not_all_reserved");
        }
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, SalesOrder.Status.SHIPPING.name())
                .set(SALES_ORDER.RANKING, UNRANKED_ORDER_VALUE)//so it falls off the radar
                .where(SALES_ORDER.OWNER_ID.eq(accountId), SALES_ORDER.ID.eq(orderId),
                        SALES_ORDER.STATUS.in(SUBMITTED.name(), PROCESSING.name(), READY_TO_SHIP.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
        removeReservedFromInventory(accountId, orderId);
    }

    @SystemEventGenerator(section = SystemEvent.Section.SALES, transition = "DELIVERED")
    public void markOrderAsDelivered(UUID accountId, @SystemEventTargetEntityId UUID orderId) {
        if (notAllItemsReserved(orderId)) {
            throw new BusinessException("Not all items are reserved.", "not_all_reserved");
        }
        int updated = db.update(SALES_ORDER)
                .set(SALES_ORDER.STATUS, SalesOrder.Status.DELIVERED.name())
                .set(SALES_ORDER.RANKING, UNRANKED_ORDER_VALUE)//so it falls off the radar
                .where(SALES_ORDER.OWNER_ID.eq(accountId), SALES_ORDER.ID.eq(orderId),
                        SALES_ORDER.STATUS.in(SUBMITTED.name(), PROCESSING.name(), READY_TO_SHIP.name(), SalesOrder.Status.SHIPPING.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
        removeReservedFromInventory(accountId, orderId);
    }

    private void removeReservedFromInventory(UUID accountId, UUID orderId) {
        var order = order(orderId);
        inventory.removeReservedForSalesOrder(accountId, orderId,
                order.items().stream()
                        .filter(item -> item.productId() != null)
                        .collect(Collectors.toMap(SalesOrder.Item::productId,
                                item -> Tuple.of(
                                        item.price().subtract(item.price().multiply(item.discount() != null ? item.discount() : BigDecimal.ZERO)),
                                        item.quantity()
                                ))));
    }

    public Tuple.Tuple2<UUID, String> manufactureMissing(UUID owner, UUID orderId,
                                                 UUID productId, UUID serviceId, BigDecimal quantity,
                                                 String customizationNote,
                                                 List<Tuple.Tuple2<String, ByteArrayInputStream>> files) {
        SalesOrder order = order(orderId);
        if (order.status().equals(SUBMITTED)) {
            db.update(SALES_ORDER)
                    .set(SALES_ORDER.STATUS, PROCESSING.name())
                    .where(SALES_ORDER.ID.eq(order.id()))
                    .execute();
        }
        var productionDeadline = order.deliveryDeadline() != null
                ? (order.deliveryDeadline().minusDays(account(owner).settings().general().defaultDeliveryTimeForSalesOrders()).isAfter(LocalDateTime.now())
                ? order.deliveryDeadline().minusDays(account(owner).settings().general().defaultDeliveryTimeForSalesOrders())
                : LocalDateTime.now())
                : null;
        var toManufacture = productId != null
                ? order.items().stream().filter(item -> productId.equals(item.productId())).findFirst().orElseThrow()
                : order.items().stream().filter(item -> serviceId.equals(item.serviceId())).findFirst().orElseThrow();
        var productDefinition = productId != null ? productDefinition(productId) : null;
        var configurableProduct = productId != null && productDefinition.details.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).anyMatch(m -> m.configurableWithOptions() || m.optional());
        var customProduct = isNotBlank(toManufacture.customizationNote()) || isNotBlank(customizationNote) || !files.isEmpty();
        if (customProduct && productId != null) {
            //we clear what was reserved as the product is customized here
            inventory.clearReserved(orderId, null, owner, new InventoryService.Stock(productId, quantity));
            //stock requirements have changed so we need to re-reserve and re-distribute stock by simulating a ranking change
            applyRanking(owner, db.select(SALES_ORDER.ID).from(SALES_ORDER)
                            .where(SALES_ORDER.OWNER_ID.eq(owner),
                                    SALES_ORDER.RANKING.gt(select(SALES_ORDER.RANKING).from(SALES_ORDER).where(SALES_ORDER.ID.eq(orderId))))
                            .orderBy(SALES_ORDER.RANKING)
                            .fetchInto(UUID.class),
                    false);
        }
        try {
            if (productId != null) {
                var mo = manufacturing.orderManufacturing(owner, orderId, productionDeadline, productId, serviceId, quantity,
                        customProduct,
                        String.join(" ", Stream.of(toManufacture.customizationNote(), customizationNote).filter(StringUtils::isNotBlank).toList()),
                        isEmpty(toManufacture.customizations())
                                ? (configurableProduct
                                ? productDefinition.details.manufacturingOperations().stream().flatMap(op -> op.materials().stream())
                                .filter(RequiredMaterial::configurableWithOptions)
                                .map(m -> Tuple.of(m.materialIds().getFirst(), m.quantity()))
                                .toList()
                                : List.of())
                                : toManufacture.customizations().stream().map(c -> Tuple.of(c.materialId(), c.quantity())).toList(),
                        files);
                return Tuple.of(mo.id(), mo.number());
            } else {
                var mo = servicing.orderServicing(owner, orderId, productionDeadline, serviceId, quantity,
                        String.join(" ", Stream.of(toManufacture.customizationNote(), customizationNote).filter(StringUtils::isNotBlank).toList()),
                        files);
                return Tuple.of(mo.id(), mo.number());
            }
        } catch (DuplicateKeyException e) {
            throw new BusinessException("Manufacturing for this product is already ordered.", "manufacturing_already_ordered");
        }
    }

    public void addToWishlist(UUID ownerId, UUID orderId, UUID productId, BigDecimal quantity, UUID supplierId) {
        SalesOrder order = order(orderId);
        if (order.items().isEmpty()
                || order.items().stream()
                .noneMatch(item -> item.productId() != null && item.productId().equals(productId))) {
            throw new BusinessException("product is not present on order", "product_not_present");
        }
        var updatedItems = order.items().stream()
                .map(item -> {
                    if (item.productId() != null && item.productId().equals(productId)) {
                        return new SalesOrder.Item(item.productId(), item.serviceId(), item.quantity(), item.price(),
                                item.discount(), item.customizationNote(), item.customizations(), true);
                    } else {
                        return item;
                    }
                })
                .toList();
        var updateStatement = db.update(SALES_ORDER)
                .set(SALES_ORDER.ITEMS, JSONB.jsonb(Json.write(updatedItems)));
        if (order.status().equals(SUBMITTED)) {
            updateStatement = updateStatement.set(SALES_ORDER.STATUS, PROCESSING.name());
        }
        updateStatement.where(SALES_ORDER.OWNER_ID.eq(ownerId), SALES_ORDER.ID.eq(order.id()))
                .execute();

        purchasesService.addToWishlist(ownerId,
                List.of(new PurchaseWishlistItem(null, null, null, false, null,
                                productId, quantity, null, supplierId)));
    }

    public Invoice invoice(UUID ownerId, UUID orderId, boolean proforma, String notes, EmailMessage emailMessage, Invoice.RenderingDetails renderingDetails) {
        var order = order(orderId);
        return invoicing.create(ownerId, order.customerId(), orderId, proforma, notes,
                order.items().stream().map(item -> new Invoice.Item(
                        item.productId(),
                        item.serviceId(),
                        item.serviceId() != null ? serviceName(item.serviceId()) : productDefinition(item.productId()).name,
                        item.quantity(),
                        measurementUnit(item),
                        item.price(),
                        item.discount(),
                        null
                )).toList(),
                emailMessage,
                renderingDetails
        );
    }

    public Tuple.Tuple3<Invoice, String, byte[]> invoicePreview(UUID ownerId, UUID orderId, boolean proforma, String notes, Invoice.RenderingDetails renderingDetails) {
        var order = order(orderId);
        var vatCalculator = vatCalculatorProvider.with(ownerId, order.customerId());
        var invoice = new Invoice(
                null, null, null, false,
                ownerId, "DRAFT",
                order.customerId(),
                LocalDate.now().plusDays(30),
                false,
                proforma,
                null,
                notes,
                order.items().stream().map(item -> new Invoice.Item(
                        item.productId(),
                        item.serviceId(),
                        item.serviceId() != null ? serviceName(item.serviceId()) : productDefinition(item.productId()).name,
                        item.quantity(),
                        measurementUnit(item),
                        item.price(),
                        item.discount(),
                        vatCalculator.itemVatRate(item.productId())
                )).toList(),
                renderingDetails
        );
        var invoiceHtml = invoicing.html(invoice);
        return Tuple.of(invoice, invoiceHtml, invoicing.pdf(invoiceHtml));
    }

    public String html(SalesOrder order, LocalDate issueDate, LocalDate expirationDate) {
        var account = account(order.ownerId());
        var customer = customer(order.customerId());
        var renderingDetails = order.renderingDetails();
        var localizer = new Localizer((renderingDetails != null && renderingDetails.language() != null) ? new Locale(renderingDetails.language()) : new Locale(account.information().address().country()));
        var vatCalculator = vatCalculatorProvider.with(account, customer);
        var currency = order.items().stream()
                .filter(item -> item.price() != null)
                .map(item -> item.price().currency())
                .findFirst().orElse(account.settings().general().defaultCurrency());
        var subTotal = new AtomicLong();
        var discountAmount = new AtomicLong();
        var vat = new AtomicLong();
        var itemIndexer = new AtomicInteger();
        var orderMap = new HashMap<>();
        orderMap.put("quote", order.status() == IN_QUOTATION);
        orderMap.put("number", order.number());
        orderMap.put("creationDate", issueDate != null ? localizer.localizeDate(issueDate) : orderSubmittedDate(order.id()).map(localizer::localizeDate).orElse(localizer.localizeDate(order.createTime())));
        orderMap.put("expirationDate", expirationDate != null ? localizer.localizeDate(expirationDate)
                : localizer.localizeDate((issueDate != null ? issueDate : orderSubmittedDate(order.id()).orElse(order.createTime().atZone(ZoneId.systemDefault()).toLocalDate())).plusDays(14)));
        orderMap.put("items", order.items().stream()
                .map(item -> {
                    var itemSubtotal = item.quantity().multiply(BigDecimal.valueOf(item.price().amount())).longValue();
                    subTotal.addAndGet(itemSubtotal);
                    var itemDiscountAmount = item.discount() != null
                            ? item.discount().multiply(BigDecimal.valueOf(itemSubtotal)).longValue()
                            : 0;
                    discountAmount.addAndGet(itemDiscountAmount);
                    var itemVatRate = vatCalculator.itemVatRate(item.productId());
                    var itemVatAmount = vatCalculator.calculate(itemSubtotal - itemDiscountAmount, itemVatRate);
                    vat.addAndGet(itemVatAmount);
                    return Map.of(
                            "index", itemIndexer.incrementAndGet(),
                            "name", item.serviceId() != null ? serviceName(item.serviceId()) : productName(item.productId()),
                            "quantity", item.quantity(),
                            "measurementUnit", localizer.localizedValue(measurementUnit(item).symbol()),
                            "price", item.price().displayAmount(),
                            "discount", item.discount() != null ? item.discount().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_EVEN) + "%" : "",
                            "VAT", itemVatRate.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_EVEN) + "%",
                            "vatAmount", new Money(itemVatAmount, currency).displayAmount(),
                            "value", new Money(itemSubtotal - itemDiscountAmount, currency).displayAmount()
                    );
                })
                .toList());
        orderMap.put("totalUnits", order.items().stream()
                .map(SalesOrder.Item::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        orderMap.put("subTotalAmount", new Money(subTotal.get(), currency).displayAmount());
        orderMap.put("discountAmount", new Money(discountAmount.get(), currency).displayAmount());
        orderMap.put("taxAmount", new Money(vat.get(), currency).displayAmount());
        orderMap.put("totalAmount", new Money(subTotal.get() - discountAmount.get() + vat.get(), currency));
        orderMap.put("notes", order.customerNotes());
        var map = new HashMap<String, Object>();
        map.put("order", orderMap);
        map.put("supplier", Map.of(
                "name", account.name(),
                "identificationNumber", account.information().identificationNumber() != null ? account.information().identificationNumber() : "",
                "taxIdentificationNumber", account.information().taxIdentificationNumber() != null ? account.information().taxIdentificationNumber() : "",
                "address", account.information().address(),
                "email", account.information().email() != null ? account.information().email() : ""
        ));
        map.put("client", Map.of(
                "name", customer.name(),
                "billingAddress", customer.details().addresses()
                        .stream()
                        .filter(address -> address.types().contains(Address.Type.BILLING))
                        .findFirst()
                        .orElse(Address.EMPTY),
                "shippingAddress", order.shippingAddress()
        ));
        map.put("labels", localizer.labels());
        map.put("base64Logo", account.logo() != null ? account.logo() : "");
        map.put("currency", currency);
        map.put("showDiscount", renderingDetails == null || renderingDetails.columnsToHide() == null || !renderingDetails.columnsToHide().contains(SalesOrder.RenderingDetails.HideableColumn.DISCOUNT));

        return templates.render("pdf/quote.html", map);
    }

    public SalesOrder attach(UUID ownerId, UUID orderId, String name, byte[] bytes) {
        return attach(ownerId, orderId, name, bytes, SalesOrderFileType.DEFAULT);
    }

    public SalesOrder attach(UUID ownerId, UUID orderId, String name, byte[] bytes, SalesOrderFileType fileType) {
        var order = order(ownerId, orderId);//we do this here to validate access
        var fileId = objectStorage.store(ownerId, name, bytes);
        db.insertInto(SALESORDER_FILE)
                .set(SALESORDER_FILE.OWNER_ID, ownerId)
                .set(SALESORDER_FILE.SALES_ORDER_ID, orderId)
                .set(SALESORDER_FILE.FILE_ID, fileId)
                .set(SALESORDER_FILE.TYPE, fileType.name())
                .execute();
        return order;
    }

    public SalesOrder deleteFile(UUID ownerId, UUID orderId, UUID fileId) {
        var order = order(ownerId, orderId);//we do this here to validate access
        db.deleteFrom(SALESORDER_FILE)
                .where(SALESORDER_FILE.OWNER_ID.eq(ownerId), SALESORDER_FILE.SALES_ORDER_ID.eq(orderId), SALESORDER_FILE.FILE_ID.eq(fileId))
                .execute();
        objectStorage.delete(ownerId, fileId);
        return order;
    }

    public GoodsAccompanyingNote goodsAccompanyingNote(UUID ownerId, UUID orderId, LocalDate deliveryDate,
                                                       Address from, Address to, UUID delegateId,
                                                       String transportRegistrationNumber, String notes, List<GoodsAccompanyingNote.Item> items) {
        var order = order(ownerId, orderId);
        //validate the items
        if (items.stream().anyMatch(item -> order.items().stream().noneMatch(i -> item.id().equals(i.productId())))
                || items.stream().anyMatch(item -> order.items().stream()
                .anyMatch(i -> item.id().equals(i.productId()) && item.quantity().compareTo(i.quantity()) > 0))) {
            throw new BusinessException("Invalid items for goods accompanying note.", "product_not_present");
        }
        var note = goodsAccompanyingNotesService.create(ownerId, deliveryDate, from, to,
                delegateId, transportRegistrationNumber, notes,
                items.stream()
                        .map(item -> new GoodsAccompanyingNote.Item(item.id(), item.quantity(),
                                //add the price from the SO
                                order.items().stream()
                                        .filter(i -> item.id().equals(i.productId())).findFirst()
                                        .map(SalesOrder.Item::price)
                                        .orElseThrow(),
                                //add the discount from the SO
                                order.items().stream()
                                        .filter(i -> item.id().equals(i.productId())).findFirst()
                                        .map(SalesOrder.Item::discount)
                                        .orElse(null)))
                        .toList());
        db.insertInto(SALESORDER_GOODSACCOMPANYINGNOTE)
                .set(SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID, orderId)
                .set(SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID, note.id())
                .execute();
        return note;
    }

    public String previewGoodsAccompanyingNote(UUID ownerId, UUID orderId, LocalDate deliveryDate,
                                               Address from, Address to, UUID delegateId,
                                               String transportRegistrationNumber, String notes, List<GoodsAccompanyingNote.Item> items) {
        var order = order(ownerId, orderId);
        //validate the items
        if (items.stream().anyMatch(item -> order.items().stream().noneMatch(i -> item.id().equals(i.productId())))
                || items.stream().anyMatch(item -> order.items().stream()
                .anyMatch(i -> item.id().equals(i.productId()) && item.quantity().compareTo(i.quantity()) > 0))) {
            throw new BusinessException("Invalid items for goods accompanying note.", "product_not_present");
        }
        return goodsAccompanyingNotesService.html(order.id(),
                new GoodsAccompanyingNote(
                        null, Instant.now(), null, false,
                        ownerId,
                        "00001",
                        deliveryDate,
                        from, to,
                        delegateId,
                        transportRegistrationNumber,
                        notes,
                        items.stream()
                                .map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(),
                                        order.items().stream().filter(oi -> oi.productId() != null && oi.productId().equals(i.id())).findFirst().map(SalesOrder.Item::price).orElseThrow(),
                                        order.items().stream().filter(oi -> oi.productId() != null && oi.productId().equals(i.id())).findFirst().map(SalesOrder.Item::discount).orElse(null)))
                                .toList()
                ));
    }


    public MeasurementUnit measurementUnit(SalesOrder.Item item) {
        if (item.productId() != null) {
            return measurementUnit(item.productId());
        } else if (item.serviceId() != null) {
            return measurementUnitForService(item.serviceId());
        }
        throw new RuntimeException("Unknown measurement unit");
    }

    public SalesOrder saveVersion(UUID ownerId, UUID orderId, LocalDate offerExpiration, String customerNotes, SalesOrder.RenderingDetails renderingDetails) {
        var order = order(ownerId, orderId);
        var account = account(ownerId);
        db.update(SALES_ORDER)
                .set(SALES_ORDER.OFFER_DATE, account.nowForAccount())
                .set(SALES_ORDER.OFFER_EXPIRATION, offerExpiration.atStartOfDay())
                .set(SALES_ORDER.CUSTOMER_NOTES, customerNotes)
                .set(SALES_ORDER.RENDERING_DETAILS, JSONB.valueOf(Json.write(renderingDetails)))
                .where(SALES_ORDER.ID.eq(orderId), SALES_ORDER.OWNER_ID.eq(ownerId))
                .execute();
        var html = html(order.setRenderingDetails(renderingDetails).setCustomerNotes(customerNotes), account.nowForAccount().toLocalDate(), offerExpiration);
        return attach(ownerId, orderId,order.number() + "_" + DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm").format(LocalDateTime.now()) + ".html", html.getBytes(), SalesOrderFileType.VERSION);
    }

    public SalesOrder clone(UUID ownerId, UUID orderId) {
        var order = order(ownerId, orderId);
        return createSalesOrder(ownerId, order.customerId(), LocalDateTime.now(), null,
                order.status() == IN_QUOTATION ? IN_QUOTATION : SUBMITTED,
                order.items(), order.shippingAddress(), order.notes(), order.customerNotes(), null, null, order.renderingDetails());
    }

    private MeasurementUnit measurementUnit(UUID materialGoodId) {
        return db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialGoodId))
                .fetchSingleInto(MaterialGood.Details.class)
                .measurementUnit();
    }

    private MeasurementUnit measurementUnitForService(UUID serviceId) {
        return db.select(SERVICE_TEMPLATE.MEASUREMENT_UNIT)
                .from(SERVICE_TEMPLATE)
                .where(SERVICE_TEMPLATE.ID.eq(serviceId))
                .fetchSingleInto(MeasurementUnit.class);
    }

    private String productName(UUID id) {
        return db.select(MATERIAL_GOOD.NAME)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(id))
                .fetchSingleInto(String.class);
    }

    public byte[] pdf(String html) {
        return htmlToPdfConverter.convert(html);
    }

    public Note addNote(UUID ownerId, UUID orderId, UUID userId, String note) {
        var id = UUID.randomUUID();
        db.insertInto(NOTE)
                .set(NOTE.ID, id)
                .set(NOTE.OWNER_ID, ownerId)
                .set(NOTE.ADDED_BY_ID, userId)
                .set(NOTE.NOTE_, note)
                .execute();
        db.insertInto(SALES_ORDER_NOTE)
                .set(SALES_ORDER_NOTE.NOTE_ID, id)
                .set(SALES_ORDER_NOTE.SALES_ORDER_ID, orderId)
                .execute();
        userTaggingProcessor.notify(ownerId, Notification.Section.SALES, orderId, userId, id, note, number(orderId));
        return new Note(id, Instant.now(), Instant.now(), false, ownerId, userId, note);
    }

    public boolean allItemsAvailable(UUID ownerId, UUID orderId) {
        return order(ownerId, orderId).items().stream()
                .filter(item -> item.productId() != null)
                .allMatch(item -> item.quantity().compareTo(db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), BigDecimal.ZERO))
                        .from(RESERVED_INVENTORY)
                        .where(RESERVED_INVENTORY.SALES_ORDER_ID.eq(orderId), RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(item.productId()))
                        .fetchSingleInto(BigDecimal.class)) <= 0);
    }

    public Optional<LocalDate> orderSubmittedDate(UUID orderId) {
        return db.select(SYSTEM_EVENT.CREATE_TIME)
                .from(SYSTEM_EVENT)
                .where(SYSTEM_EVENT.TARGET_ENTITY_ID.eq(orderId), SYSTEM_EVENT.TRANSITION.eq(SUBMITTED.name()))
                .fetchOptionalInto(LocalDateTime.class)
                .map(LocalDateTime::toLocalDate);
    }

    private Account account(UUID id) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(id)).fetchSingleInto(Account.class);
    }

    private Company customer(UUID id) {
        return db.selectFrom(COMPANY)
                .where(COMPANY.ID.eq(id))
                .fetchSingleInto(Company.class);
    }

    private Map<UUID, BigDecimal> reserveStock(UUID ownerId, List<SalesOrder.Item> items, UUID orderId) {
        return inventory.reserveStockForSalesOrder(ownerId, orderId,
                items.stream()
                        .filter(item -> item.productId() != null)
                        .filter(item -> isBlank(item.customizationNote()))
                        .filter(item -> productDefinition(item.productId()).details.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).noneMatch(m -> m.optional() || m.configurableWithOptions()))
                        .map(product -> new InventoryService.Stock(product.productId(), product.quantity()))
                        .collect(toList()));
    }

    private MaterialGood productDefinition(UUID productId) {
        return db.selectFrom(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(productId))
                .fetchSingle()
                .into(MaterialGood.class);
    }

    private SalesOrder order(UUID ownerId, UUID orderId) {
        return db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.OWNER_ID.eq(ownerId), SALES_ORDER.ID.eq(orderId))
                .fetchSingle()
                .into(SalesOrder.class);
    }

    private SalesOrder order(UUID orderId) {
        return db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(orderId))
                .fetchSingle()
                .into(SalesOrder.class);
    }

    private int getRanking(UUID ownerId) {
        return ofNullable(db.select(max(SALES_ORDER.RANKING))
                .from(SALES_ORDER)
                .where(SALES_ORDER.OWNER_ID.eq(ownerId))
                .fetch().getFirst().value1())
                .orElse(UNRANKED_ORDER_VALUE);
    }


    private String serviceName(UUID serviceId) {
        return db.select(SERVICE_TEMPLATE.NAME)
                .from(SERVICE_TEMPLATE)
                .where(SERVICE_TEMPLATE.ID.eq(serviceId))
                .fetchSingleInto(String.class);
    }

    private boolean notAllItemsReserved(UUID orderId) {
        var order = order(orderId);
        if (order.status() == SHIPPING) {//this order was already shipped so no need to check this again
            return false;
        }
        return order.items().stream()
                .filter(item -> item.productId() != null)
                .anyMatch(item -> item.quantity().compareTo(db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                        .from(RESERVED_INVENTORY)
                        .where(RESERVED_INVENTORY.SALES_ORDER_ID.eq(orderId), RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(item.productId()))
                        .fetchSingleInto(BigDecimal.class)) != 0);
    }

    private String number(UUID orderId) {
        return db.select(SALES_ORDER.NUMBER)
                .from(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(orderId))
                .fetchSingleInto(String.class);
    }

    public enum SalesOrderFileType {DEFAULT, VERSION}

}
