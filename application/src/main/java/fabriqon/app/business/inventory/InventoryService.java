package fabriqon.app.business.inventory;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.accounts.AccountService;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.inventory.accounting.AccountingStrategyProvider;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.purchases.PurchaseOrder;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.response.Entity;
import fabriqon.events.Events;
import fabriqon.jooq.classes.tables.records.ReservedInventoryRecord;
import fabriqon.misc.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static fabriqon.jooq.classes.Tables.*;
import static java.util.stream.Collectors.groupingBy;
import static org.apache.commons.lang3.BooleanUtils.isFalse;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.jooq.impl.DSL.*;

@Slf4j
@Component
@Transactional
public class InventoryService {

    private final DSLContext db;
    private final AccountService accountService;
    private final AccountingStrategyProvider accountingStrategyProvider;
    private final Events events;

    @Autowired
    public InventoryService(DSLContext db, AccountService accountService,
                            AccountingStrategyProvider accountingStrategyProvider, Events events) {
        this.db = db;
        this.accountService = accountService;
        this.accountingStrategyProvider = accountingStrategyProvider;
        this.events = events;
    }

    UUID registerEntry(InventoryEntry entry) {
        final var id = UUID.randomUUID();
        db.insertInto(INVENTORY)
                .set(INVENTORY.ID, id)
                .set(INVENTORY.OWNER_ID, entry.ownerId())
                .set(INVENTORY.MATERIAL_GOOD_ID, entry.materialGoodId())
//                .set(INVENTORY.LOCATION_ID, entry.locationId())
                .set(INVENTORY.SUPPLIER_ID, entry.supplierId())
                .set(INVENTORY.UNIT_ID, entry.unitId())
                .set(INVENTORY.INVENTORY_ADJUSTMENT_ORDER_ID, entry.inventoryAdjustmentOrderId())
                .set(INVENTORY.SALES_ORDER_ID, entry.salesOrderId())
                .set(INVENTORY.MANUFACTURING_ORDER_ID, entry.manufacturingOrderId())
                .set(INVENTORY.PURCHASE_DATE, entry.purchaseDate() != null ? entry.purchaseDate() : null)
                .set(INVENTORY.EXPIRY_DATE, entry.expiryDate() != null ? entry.expiryDate() : null)
                .set(INVENTORY.RECEPTION_PRICE_AMOUNT, entry.receptionPrice() != null ? entry.receptionPrice().amount() : null)
                .set(INVENTORY.RECEPTION_PRICE_CURRENCY, entry.receptionPrice() != null ? entry.receptionPrice().currency() : null)
                .set(INVENTORY.EXIT_PRICE_AMOUNT, entry.exitPrice() != null ? entry.exitPrice().amount() : null)
                .set(INVENTORY.EXIT_PRICE_CURRENCY, entry.exitPrice() != null ? entry.exitPrice().currency() : null)
                .set(INVENTORY.QUANTITY, entry.quantity())
                .set(INVENTORY.RECEPTION_RECEIPT_ID, entry.receptionReceiptId())
                .execute();
        events.publish(new InventoryChangedEvent(new InventoryChangedEvent.Data(entry.ownerId(), id)));
        return id;
    }


    public Map<UUID, BigDecimal> reserveStockForServicingOrder(UUID accountId, UUID orderId, List<Stock> items) {
        return reserveStock(null, null, orderId, accountId, items, true, null);
    }

    public Map<UUID, BigDecimal> reserveStockForManufacturingOrder(UUID accountId, UUID orderId, List<Stock> items, boolean mandatoryForOrder, UUID fromOrder) {
        return reserveStock(null, orderId, null, accountId, items, mandatoryForOrder, fromOrder);
    }

    public Map<UUID, BigDecimal> reserveStockForSalesOrder(UUID accountId, UUID orderId, List<Stock> items) {
        return reserveStock(orderId, null, null, accountId, items, false, null);
    }
    public Map<UUID, BigDecimal> reserveStockForSalesOrder(UUID accountId, UUID orderId, List<Stock> items, boolean mandatoryForOrder, UUID fromOrder) {
        return reserveStock(orderId, null, null, accountId, items, mandatoryForOrder, fromOrder);
    }

    private Map<UUID, BigDecimal> reserveStock(UUID salesOrder, UUID manufacturingOrder, UUID servicingOrder,
                                   UUID owner, List<Stock> items,
                                   boolean mandatoryForOrder, UUID fromOrder) {

        var unitId = defaultInventoryUnitId(owner);
        log.info("reserving stock for SO [{}]; MO [{}]; ServicingO [{}]; items [{}]; mandatory [{}]; from [{}]; unit [{}]",
                salesOrder, manufacturingOrder, servicingOrder, items, mandatoryForOrder, fromOrder, unitId);
        var notAvailableItems = new HashMap<UUID, BigDecimal>();
        items.forEach(
                item -> {
                    var allOnInventory = getCurrentStockByTypes(owner, item.materialGoodId, List.of(unitId)).getOrDefault(unitId, BigDecimal.ZERO);
                    var reserved = getReservedStockByUnits(owner, item.materialGoodId, List.of(unitId)).getOrDefault(unitId, BigDecimal.ZERO);
                    var available = allOnInventory.subtract(reserved);
                    if (item.quantity.compareTo(available) > 0) {
                        notAvailableItems.put(item.materialGoodId, item.quantity.subtract(available.max(BigDecimal.ZERO)));
                    }
                    if (available.compareTo(BigDecimal.ZERO) > 0) {
                        var toReserve = item.quantity().min(available);
                        //we do a check so we don't reserve more than we should
                        if (salesOrder != null && toReserve.add(
                                        db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                                                .from(RESERVED_INVENTORY)
                                                .where(RESERVED_INVENTORY.SALES_ORDER_ID.eq(salesOrder), RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(item.materialGoodId))
                                                .fetchSingleInto(BigDecimal.class))
                                .compareTo(
                                        db.selectFrom(SALES_ORDER)
                                                .where(SALES_ORDER.ID.eq(salesOrder))
                                                .fetchSingleInto(SalesOrder.class)
                                                .items().stream()
                                                .filter(i -> item.materialGoodId.equals(i.productId()))
                                                .findFirst()
                                                .map(SalesOrder.Item::quantity).orElseThrow()) > 0) {
                            throw new RuntimeException("can't reserve [" + toReserve + "] for sales order [" + salesOrder + "]");
                        }
                        db.insertInto(RESERVED_INVENTORY)
                                .set(RESERVED_INVENTORY.ID, UUID.randomUUID())
                                .set(RESERVED_INVENTORY.OWNER_ID, owner)
                                .set(RESERVED_INVENTORY.SALES_ORDER_ID, salesOrder)
                                .set(RESERVED_INVENTORY.MANUFACTURING_ORDER_ID, manufacturingOrder)
                                .set(RESERVED_INVENTORY.SERVICING_ORDER_ID, servicingOrder)
//                            .set(RESERVED_INVENTORY.LOCATION_ID, location id) TODO
                                .set(RESERVED_INVENTORY.INVENTORY_UNIT_ID, unitId)
                                .set(RESERVED_INVENTORY.MATERIAL_GOOD_ID, item.materialGoodId())
                                .set(RESERVED_INVENTORY.QUANTITY, toReserve)
                                .set(RESERVED_INVENTORY.MANDATORY_FOR_ORDER, mandatoryForOrder)
                                .set(RESERVED_INVENTORY.FROM_ORDER, fromOrder)
                                .execute();
                    }
                }
        );
        return notAvailableItems;
    }

    public BigDecimal getReservedStock(UUID owner, UUID materialGoodId, UUID typeId) {
        return getReservedStock(owner, materialGoodId, List.of(typeId));
    }

    public BigDecimal getReservedStock(UUID owner, UUID materialGoodId, List<UUID> typeIds) {
        return getReservedStockByUnits(owner, materialGoodId, typeIds)
                .values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    public Map<UUID, BigDecimal> getReservedStockByUnits(UUID owner, UUID materialGoodId, List<UUID> unitIds) {
        return (Map) db.select(RESERVED_INVENTORY.INVENTORY_UNIT_ID, coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0)).from(RESERVED_INVENTORY)
                .where(
                        RESERVED_INVENTORY.OWNER_ID.eq(owner),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(materialGoodId),
                        RESERVED_INVENTORY.INVENTORY_UNIT_ID.in(unitIds)
                )
                .groupBy(RESERVED_INVENTORY.INVENTORY_UNIT_ID)
                .fetch()
                .intoMap(0, 1);
    }

    /**
     * Remove all reserved stock from the inventory; we also ensure that we are not removing more than it is specified in the order by mistake
     */
    public void removeReservedForSalesOrder(UUID accountId, UUID salesOrderId, Map<UUID, Tuple.Tuple2<Money, BigDecimal>> exitPricesAndQuantities) {
        var reservedEntriesCondition = RESERVED_INVENTORY.OWNER_ID.eq(accountId)
                .and(RESERVED_INVENTORY.SALES_ORDER_ID.eq(salesOrderId).and(RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.isNull()));
        //remove these from the inventory
        db.selectFrom(RESERVED_INVENTORY)
                .where(reservedEntriesCondition)
                .fetch()
                .stream()
                .collect(groupingBy(ReservedInventoryRecord::getMaterialGoodId))
                .forEach((materialGood, reservedGoods) -> {
                    if (exitPricesAndQuantities.entrySet().stream()
                            .filter(e -> e.getKey().equals(materialGood))
                            .anyMatch(e ->
                                    reservedGoods.stream()
                                            .filter(r -> r.getMaterialGoodId().equals(e.getKey()))
                                            .map(ReservedInventoryRecord::getQuantity)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                                            .compareTo(e.getValue().b()) != 0
                            )) {
                        throw new RuntimeException("Cannot process sales order with id: [" + salesOrderId + "]. Too many items are being removed.");
                    }
                    var toRemove = reservedGoods.stream()
                            .filter(reservedGood -> isFalse(reservedGood.getMandatoryForOrder()))
                            .collect(groupingBy(ReservedInventoryRecord::getInventoryUnitId))
                            .entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey,
                                    e -> e.getValue().stream().map(ReservedInventoryRecord::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)));
                    toRemove.forEach((typeId, quantity) -> remove(accountId, materialGood, typeId, quantity)
                            .forEach(e -> registerEntry(e.setSalesOrderId(salesOrderId).setExitPrice(exitPricesAndQuantities.get(materialGood).a()))));

                    //now we handle mandatory for order records where we just inverse the entry's quantity without
                    // calling the accounting strategy; this assumes that there is a 1-1 relation between the reserved
                    // entry and the inventory entry
                    reservedGoods.stream()
                            .filter(reservedGood -> isTrue(reservedGood.getMandatoryForOrder()))
                            .forEach(reservedGood -> {
                                var inventoryEntryFilter = INVENTORY.OWNER_ID.eq(accountId)
                                        .and(INVENTORY.MATERIAL_GOOD_ID.eq(reservedGood.getMaterialGoodId()))
                                        .and(INVENTORY.UNIT_ID.eq(reservedGood.getInventoryUnitId()))
                                        .and(INVENTORY.MANUFACTURING_ORDER_ID.eq(reservedGood.getFromOrder()));
                                var entry = db.selectFrom(INVENTORY)
                                        .where(inventoryEntryFilter)
                                        .fetchSingleInto(InventoryEntry.class);
                                if (entry.quantity().compareTo(reservedGood.getQuantity()) != 0) {
                                    throw new BusinessException("quantities don't match between reserved and inventory", "quantity_mismatch");
                                }
                                registerEntry(entry
                                        .setSalesOrderId(salesOrderId)
                                        .setExitPrice(exitPricesAndQuantities.get(reservedGood.getMaterialGoodId()).a())
                                        .setQuantity(entry.quantity().multiply(BigDecimal.valueOf(-1))));
                            });
                });

        //remove them from the reserved table
        db.deleteFrom(RESERVED_INVENTORY).where(reservedEntriesCondition).execute();
    }

    public void removeForManufacturingOrder(UUID ownerId, UUID materialId, UUID inventoryUnitId, BigDecimal quantity, UUID orderId) {
        remove(ownerId, materialId, inventoryUnitId, quantity)
                .forEach(e -> registerEntry(e.setManufacturingOrderId(orderId)));
    }

    public void removeForServicingOrder(UUID ownerId, UUID materialId, UUID inventoryUnitId, BigDecimal quantity, UUID orderId) {
        remove(ownerId, materialId, inventoryUnitId, quantity)
                .forEach(e -> registerEntry(e.setServicingOrderId(orderId)));
    }

    public void removeForAdjustmentOrder(UUID ownerId, UUID materialId, UUID typeId, BigDecimal quantity, UUID orderId) {
        remove(ownerId, materialId, typeId, quantity)
                .forEach(e -> registerEntry(e.setInventoryAdjustmentOrderId(orderId)));
    }

    private List<InventoryEntry> remove(UUID ownerId, UUID materialId, UUID typeId, BigDecimal quantity) {
        return accountingStrategyProvider.strategy(accountService.accountingMethod(ownerId))
                .remove(ownerId, materialId, quantity, typeId);
    }

    public void clearReservedForServicingOrder(UUID accountId, UUID servicingOrderId) {
        log.info("clearing reserved for servicing order [{}]", servicingOrderId);
        db.deleteFrom(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(accountId),
                        RESERVED_INVENTORY.SERVICING_ORDER_ID.eq(servicingOrderId))
                .execute();
    }

    public void clearReservedForManufacturingOrder(UUID accountId, UUID manufacturingOrderId, boolean includeMandatoryForOrder) {
        log.info("clearing reserved for manufacturing order [{}]", manufacturingOrderId);
        db.deleteFrom(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(accountId),
                        RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.eq(manufacturingOrderId),
                        includeMandatoryForOrder ? RESERVED_INVENTORY.MANDATORY_FOR_ORDER.isNotNull() : RESERVED_INVENTORY.MANDATORY_FOR_ORDER.isFalse())
                .execute();
    }

    public void clearReservedForSalesOrder(UUID accountId, UUID salesOrderId) {
        log.info("clearing reserved for sales order [{}]", salesOrderId);
        db.deleteFrom(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(accountId),
                        RESERVED_INVENTORY.SALES_ORDER_ID.eq(salesOrderId))
                .execute();
    }

    /**
     * Clear part of the stock that is reserved for the order
     */
    public BigDecimal clearReserved(UUID salesOrderId, UUID manufacturingOrderId, UUID ownerId, Stock stock) {
        var reservedEntriesCondition = RESERVED_INVENTORY.OWNER_ID.eq(ownerId)
                .and(salesOrderId != null
                        ? RESERVED_INVENTORY.SALES_ORDER_ID.eq(salesOrderId).and(RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.isNull())
                        : RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.eq(manufacturingOrderId).and(RESERVED_INVENTORY.SALES_ORDER_ID.isNull()))
                //we can't clear reservations that are mandatory for the order
                .and(RESERVED_INVENTORY.MANDATORY_FOR_ORDER.isFalse());

        Queue<ReservedInventoryRecord> reservedEntries = new LinkedList<>(db.selectFrom(RESERVED_INVENTORY)
                .where(reservedEntriesCondition.and(RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(stock.materialGoodId)))
                .orderBy(RESERVED_INVENTORY.CREATE_TIME.desc())
                .fetchInto(ReservedInventoryRecord.class));
        var removedQuantity = BigDecimal.ZERO;
        while (!reservedEntries.isEmpty() && removedQuantity.compareTo(stock.quantity) < 0) {
            var entry = reservedEntries.remove();
            if (entry.getQuantity().compareTo(stock.quantity.subtract(removedQuantity)) <= 0) {
                removedQuantity = removedQuantity.add(entry.getQuantity());
                db.deleteFrom(RESERVED_INVENTORY).where(RESERVED_INVENTORY.ID.eq(entry.getId())).execute();
            } else {
                var toSubtract = stock.quantity.subtract(removedQuantity);
                db.update(RESERVED_INVENTORY)
                        .set(RESERVED_INVENTORY.QUANTITY, entry.getQuantity().subtract(toSubtract))
                        .where(RESERVED_INVENTORY.ID.eq(entry.getId()))
                        .execute();
                removedQuantity = removedQuantity.add(toSubtract);
            }
        }
        return removedQuantity;
    }

    public List<UUID> fill(List<InventoryEntry> inventoryEntries) {
        return inventoryEntries.stream().map(this::fill).toList();
    }

    public UUID fill(InventoryEntry inventoryEntry) {
        if (inventoryEntry.quantity().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("quantity for fill cannot be <0", "invalid_quantity_for_fill");
        }
        return registerEntry(inventoryEntry);
    }

    public BigDecimal getCurrentStock(UUID accountId, UUID materialId) {
        return db.select(coalesce(sum(INVENTORY.QUANTITY), 0))
                .from(INVENTORY)
                .where(INVENTORY.OWNER_ID.eq(accountId).and(INVENTORY.MATERIAL_GOOD_ID.eq(materialId)))
                .fetchSingleInto(BigDecimal.class);
    }

    public BigDecimal getCurrentStock(UUID accountId, UUID materialId, UUID inventoryUnitId) {
        return getCurrentStock(accountId, materialId, List.of(inventoryUnitId));
    }

    public BigDecimal getCurrentStock(UUID accountId, UUID materialId, List<UUID> inventoryUnitIds) {
        return getCurrentStockByTypes(accountId, materialId, inventoryUnitIds)
                .values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    public Map<UUID, BigDecimal> getCurrentStockByTypes(UUID accountId, UUID materialId, List<UUID> inventoryUnitIds) {
        var filter = INVENTORY.OWNER_ID.eq(accountId).and(INVENTORY.MATERIAL_GOOD_ID.eq(materialId));
        if (isNotEmpty(inventoryUnitIds)) {
            filter = filter.and(INVENTORY.UNIT_ID.in(inventoryUnitIds));
        }
        return (Map) db.select(INVENTORY.UNIT_ID, coalesce(sum(INVENTORY.QUANTITY), 0))
                .from(INVENTORY)
                .where(filter)
                .groupBy(INVENTORY.UNIT_ID)
                .fetch()
                .intoMap(0, 1);
    }

    public Money getCurrentStockValue(UUID ownerId, UUID materialId, UUID inventoryUnitId) {
        return accountingStrategyProvider.strategy(accountService.accountingMethod(ownerId))
                .calculateStockValue(ownerId, materialId, inventoryUnitId);
    }

    public Money getAverageCost(UUID ownerId, UUID materialId) {
        return accountingStrategyProvider
                .strategy(accountService.accountingMethod(ownerId))
                .calculateAverageCostPerItem(ownerId, materialId, null);
    }

    public Money getAverageCost(UUID ownerId, UUID materialId, List<UUID> inventoryUnitIds) {
        var totalCost = inventoryUnitIds.stream()
                .map(inventoryUnitId -> accountingStrategyProvider
                        .strategy(accountService.accountingMethod(ownerId))
                        .calculateStockValue(ownerId, materialId, inventoryUnitId)
                )
                .reduce(Money::add).orElse(new Money(0, Money.DEFAULT_CURRENCY));
        var totalStock = inventoryUnitIds.stream()
                .map(inventoryUnitId -> accountingStrategyProvider
                        .strategy(accountService.accountingMethod(ownerId))
                        .calculateStock(ownerId, materialId, inventoryUnitId)
                )
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalStock.compareTo(BigDecimal.ZERO) > 0) {
            return totalCost.divide(totalStock);
        }
        return totalCost;
    }

    public Money getCostForQuantity(UUID ownerId, UUID materialId, UUID inventoryUnitId, BigDecimal quantity) {
        return accountingStrategyProvider
                .strategy(accountService.accountingMethod(ownerId))
                .calculateCostForQuantity(ownerId, materialId, inventoryUnitId, quantity);
    }

    public Account.Settings.General.InventoryAccountingSettings inventorySettings(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().inventoryAccountingSettings();
    }

    public UUID defaultInventoryUnitId(UUID ownerId) {
        var type = db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingle()
                .into(Account.class)
                .settings()
                .general()
                .inventoryAccountingSettings()
                .unitDesignations()
                .defaultInventoryUnit();
        return Objects.requireNonNull(type, "default inventory unit cannot be null");
    }

    //TODO return Tuple instead of Entity here
    public Entity lastOrderedFrom(UUID ownerId, UUID materialGoodId) {
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .where(COMPANY.OWNER_ID.eq(ownerId), COMPANY.ID.eq(
                        select(INVENTORY.SUPPLIER_ID)
                                .from(INVENTORY)
                                .where(INVENTORY.MATERIAL_GOOD_ID.eq(materialGoodId), INVENTORY.SUPPLIER_ID.isNotNull())
                                .orderBy(INVENTORY.CREATE_TIME.desc())
                                .limit(1)
                ))
                .fetchOneInto(Entity.class);
    }

    public BigDecimal getIncomingStock(UUID accountId, UUID goodId) {
        var fromPurchases = db.selectFrom(PURCHASE_ORDER)
                .where(
                        PURCHASE_ORDER.DELETED.isFalse(),
                        PURCHASE_ORDER.OWNER_ID.eq(accountId),
                        PURCHASE_ORDER.STATUS.eq(PurchaseOrder.Status.SUBMITTED.name())
                )
                .fetchInto(PurchaseOrder.class)
                .stream()
                .map(order -> order.items().stream()
                        .filter(item -> item.materialGoodId().equals(goodId))
                        .map(PurchaseOrder.Item::quantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var fromManufacturing = db.select(MANUFACTURING_ORDER.QUANTITY).from(MANUFACTURING_ORDER)
                .where(
                        MANUFACTURING_ORDER.DELETED.isFalse(),
                        MANUFACTURING_ORDER.OWNER_ID.eq(accountId),
                        MANUFACTURING_ORDER.STATUS.in(ManufacturingOrder.Status.SUBMITTED.name(), ManufacturingOrder.Status.MANUFACTURING.name()),
                        MANUFACTURING_ORDER.PRODUCT_ID.eq(goodId)
                )
                .fetchInto(BigDecimal.class)
                .stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return fromPurchases.add(fromManufacturing);
    }

    public record Stock(
            UUID materialGoodId,
            BigDecimal quantity
    ) {
    }

}
