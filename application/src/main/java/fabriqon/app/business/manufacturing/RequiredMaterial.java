package fabriqon.app.business.manufacturing;

import fabriqon.app.business.goods.Dimensions;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

public record RequiredMaterial(List<UUID> materialIds,
                               BigDecimal quantity,
                               BigDecimal wastePercentage,
                               Dimensions dimensions,
                               Dimensions totalDimensions) {

    public RequiredMaterial setQuantity(BigDecimal quantity) {
        return new RequiredMaterial(materialIds, quantity, wastePercentage, dimensions, totalDimensions);
    }
}
