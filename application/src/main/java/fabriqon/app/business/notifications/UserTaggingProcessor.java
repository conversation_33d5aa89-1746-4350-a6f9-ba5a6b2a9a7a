package fabriqon.app.business.notifications;

import fabriqon.Notification;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

import static fabriqon.jooq.classes.Tables.USERS;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
public class UserTaggingProcessor {

    private static final Pattern TAGGED_USER_REGEX = Pattern.compile("@user:([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})");
    private final DSLContext db;
    private final NotificationService notificationService;

    public UserTaggingProcessor(DSLContext db, NotificationService notificationService) {
        this.db = db;
        this.notificationService = notificationService;
    }


    public void notify(UUID ownerId, Notification.Section section, UUID targetEntityId, UUID createdByUser, UUID noteId, String note, String entityName) {
        var taggedUsers = processTaggedUsers(note);
        if (isEmpty(taggedUsers)) {
            return;
        }
        taggedUsers.forEach(taggedUser -> notificationService.create(ownerId, Notification.Type.COMMENT, section, targetEntityId, userName(createdByUser), Map.of("noteId", noteId, "name", entityName), taggedUser));
    }

    private List<UUID> processTaggedUsers(String content) {
        return TAGGED_USER_REGEX.matcher(content)
                .results()
                .map(matchResult -> UUID.fromString(matchResult.group(1)))
                .toList();
    }

    private String userName(UUID userId) {
        return db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(userId)).fetchSingleInto(String.class);
    }

}
