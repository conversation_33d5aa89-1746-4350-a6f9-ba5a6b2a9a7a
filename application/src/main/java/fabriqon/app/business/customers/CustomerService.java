package fabriqon.app.business.customers;

import fabriqon.Notification;
import fabriqon.app.business.company.CompanyService;
import fabriqon.app.business.notifications.UserTaggingProcessor;
import fabriqon.app.common.model.Company;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;

@Component
@Transactional
public class CustomerService {

    private final DSLContext db;
    private final CompanyService companyService;
    private final UserTaggingProcessor userTaggingProcessor;

    @Autowired
    public CustomerService(final DSLContext db, CompanyService companyService, UserTaggingProcessor userTaggingProcessor) {
        this.db = db;
        this.companyService = companyService;
        this.userTaggingProcessor = userTaggingProcessor;
    }

    public Company create(UUID owner,
                          Company.Type type,
                          String name,
                          Company.Details details) {
        var company = companyService.create(owner, type, name, details);
        db.insertInto(ACCOUNT_CUSTOMER)
                .set(ACCOUNT_CUSTOMER.CUSTOMER_ID, company.id())
                .set(ACCOUNT_CUSTOMER.OWNER_ID, owner)
                .execute();
        return company;
    }

    public void update(UUID ownerId, UUID id, Company.Type type, String name, Company.Details details) {
        companyService.update(ownerId, id, type, name, details);
    }

    public Note addNote(UUID ownerId, UUID customerId, UUID userId, String note) {
        var id = UUID.randomUUID();
        db.insertInto(NOTE)
                .set(NOTE.ID, id)
                .set(NOTE.OWNER_ID, ownerId)
                .set(NOTE.ADDED_BY_ID, userId)
                .set(NOTE.NOTE_, note)
                .execute();
        db.insertInto(CUSTOMER_NOTE)
                .set(CUSTOMER_NOTE.NOTE_ID, id)
                .set(CUSTOMER_NOTE.COMPANY_ID, customerId)
                .execute();
        userTaggingProcessor.notify(ownerId, Notification.Section.CUSTOMERS, customerId, userId, id, note, companyService.companyName(customerId));
        return new Note(id, Instant.now(), Instant.now(), false, ownerId, userId, note);
    }

    public Company attach(UUID ownerId, UUID customerId, String originalFilename, byte[] bytes) {
        return companyService.attach(ownerId, customerId, originalFilename, bytes);
    }

    public Company deleteFile(UUID ownerId, UUID customerId, UUID fileId) {
        return companyService.deleteFile(ownerId, customerId, fileId);
    }

}
