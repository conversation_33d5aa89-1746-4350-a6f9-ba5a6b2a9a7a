package fabriqon.app.business.company;

import fabriqon.ObjectStorage;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.ContactPerson;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;
import java.util.stream.Collectors;

import static fabriqon.jooq.JooqTextSearchFunctions.toTsVector;
import static fabriqon.jooq.classes.Tables.COMPANY;
import static fabriqon.jooq.classes.Tables.COMPANY_FILE;
import static java.time.Instant.now;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
@Transactional
public class CompanyService {

    private final DSLContext db;
    private final ObjectStorage objectStorage;

    @Autowired
    public CompanyService(DSLContext db, ObjectStorage objectStorage) {
        this.db = db;
        this.objectStorage = objectStorage;
    }

    public Company create(UUID owner,
                          Company.Type type,
                          String name,
                          Company.Details details) {

        final var id = UUID.randomUUID();
        db.insertInto(COMPANY)
                .set(COMPANY.ID, id)
                .set(COMPANY.OWNER_ID, owner)
                .set(COMPANY.COMPANY_TYPE, type.name())
                .set(COMPANY.COMPANY_NAME, name)
                .set(COMPANY.DETAILS, JSONB.valueOf(Json.write(details)))
                .set(COMPANY.TEXT_SEARCH, toTsVector(textSearchValues(name, details)))
                .execute();
        return new Company(id, now(), now(), false, owner, type, name, details);
    }

    public void update(UUID ownerId, UUID id, Company.Type type, String name, Company.Details details) {
        var updateStep = db.update(COMPANY).set(COMPANY.DELETED, false);
        if (type != null) {
            updateStep = updateStep.set(COMPANY.COMPANY_TYPE, type.name());
        }
        if (isNotBlank(name)) {
            updateStep = updateStep.set(COMPANY.COMPANY_NAME, name);
        }
        if (details.taxIdentificationNumber() != null
                || details.addresses() != null
                || details.contacts() != null
                || details.bankAccounts() != null
                || details.notes() != null
        ) {
            var fromDb = db.select(COMPANY.DETAILS).from(COMPANY)
                    .where(COMPANY.ID.eq(id), COMPANY.OWNER_ID.eq(ownerId))
                    .fetchSingleInto(Company.Details.class);
            if (details.identificationNumber() != null) {
                fromDb = new Company.Details(details.identificationNumber(), fromDb.taxIdentificationNumber(), fromDb.addresses(), fromDb.contacts(), fromDb.bankAccounts(), fromDb.notes());
            }
            if (details.taxIdentificationNumber() != null) {
                fromDb = new Company.Details(fromDb.identificationNumber(), details.taxIdentificationNumber(), fromDb.addresses(), fromDb.contacts(), fromDb.bankAccounts(), fromDb.notes());
            }
            if (details.addresses() != null) {
                fromDb = new Company.Details(fromDb.identificationNumber(), fromDb.taxIdentificationNumber(), details.addresses(), fromDb.contacts(), fromDb.bankAccounts(), fromDb.notes());
            }
            if (details.contacts() != null) {
                fromDb = new Company.Details(fromDb.identificationNumber(), fromDb.taxIdentificationNumber(), fromDb.addresses(), details.contacts(), fromDb.bankAccounts(), fromDb.notes());
            }
            if (details.bankAccounts() != null) {
                fromDb = new Company.Details(fromDb.identificationNumber(), fromDb.taxIdentificationNumber(), fromDb.addresses(), fromDb.contacts(), details.bankAccounts(), fromDb.notes());
            }
            if (details.notes() != null) {
                fromDb = new Company.Details(fromDb.identificationNumber(), fromDb.taxIdentificationNumber(), fromDb.addresses(), fromDb.contacts(), fromDb.bankAccounts(), details.notes());
            }
            updateStep = updateStep.set(COMPANY.DETAILS, JSONB.jsonb(Json.write(fromDb)));
        }
        updateStep = updateStep.set(COMPANY.TEXT_SEARCH, toTsVector(textSearchValues(name, details)));
        updateStep.where(COMPANY.ID.eq(id), COMPANY.OWNER_ID.eq(ownerId))
                .execute();
    }

    public String textSearchValues(String name, Company.Details details) {
        var v = name + " " +
               details.identificationNumber() + " " +
               details.taxIdentificationNumber() + " " +
                (isNotEmpty(details.addresses()) ? details.addresses().stream().map(Address::city).collect(Collectors.joining(" ")) :  "")  + " " +
                (isNotEmpty(details.contacts()) ? details.contacts().stream().map(ContactPerson::email).collect(Collectors.joining(" ")) :  "")  + " " +
                (isNotEmpty(details.contacts()) ? details.contacts().stream().map(ContactPerson::name).collect(Collectors.joining(" ")) :  "")  + " " +
                (isNotEmpty(details.contacts()) ? details.contacts().stream().map(ContactPerson::phoneNumber).collect(Collectors.joining(" ")) :  "");
        return v.replaceAll("'", "");
    }


    public Company attach(UUID ownerId, UUID companyId, String name, byte[] bytes) {
        var company = db.selectFrom(COMPANY).where(COMPANY.OWNER_ID.eq(ownerId), COMPANY.ID.eq(companyId)).fetchSingleInto(Company.class);
        var fileId = objectStorage.store(ownerId, name, bytes);
        db.insertInto(COMPANY_FILE)
                .set(COMPANY_FILE.OWNER_ID, ownerId)
                .set(COMPANY_FILE.COMPANY_ID, companyId)
                .set(COMPANY_FILE.FILE_ID, fileId)
                .execute();
        return company;
    }

    public Company deleteFile(UUID ownerId, UUID companyId, UUID fileId) {
        var company = db.selectFrom(COMPANY).where(COMPANY.OWNER_ID.eq(ownerId), COMPANY.ID.eq(companyId)).fetchSingleInto(Company.class);
        db.deleteFrom(COMPANY_FILE)
                .where(COMPANY_FILE.OWNER_ID.eq(ownerId), COMPANY_FILE.COMPANY_ID.eq(companyId), COMPANY_FILE.FILE_ID.eq(fileId))
                .execute();
        objectStorage.delete(ownerId, fileId);
        return company;
    }

    public String companyName(UUID id) {
        return db.select(COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .where(COMPANY.ID.eq(id))
                .fetchSingleInto(String.class);
    }
}
