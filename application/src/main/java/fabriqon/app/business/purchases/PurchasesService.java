package fabriqon.app.business.purchases;

import fabriqon.Notification;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.customers.Note;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.exceptions.ResourceNotFoundException;
import fabriqon.app.business.inventory.reception.AdditionalCost;
import fabriqon.app.business.inventory.reception.ReceivedGood;
import fabriqon.app.business.inventory.reception.ReceptionService;
import fabriqon.app.business.inventory.reception.SupportingDocument;
import fabriqon.app.business.notifications.UserTaggingProcessor;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.app.business.suppliers.SupplierService;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.ContactPerson;
import fabriqon.app.common.model.EmailMessage;
import fabriqon.app.common.model.Money;
import fabriqon.email.Email;
import fabriqon.email.EmailSender;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static fabriqon.jooq.classes.Tables.*;
import static java.util.stream.Collectors.toList;
import static org.apache.poi.util.StringUtil.isNotBlank;

@Component
@Transactional
public class PurchasesService {

    private final Sequences sequences;
    private final DSLContext db;
    private final SupplierService supplierService;
    private final EmailSender emailSender;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final Templates templates;
    private final ReceptionService receptionService;
    private final UserTaggingProcessor userTaggingProcessor;

    @Autowired
    public PurchasesService(Sequences sequences, DSLContext db,
                            SupplierService supplierService, EmailSender emailSender,
                            HtmlToPdfConverter htmlToPdfConverter, Templates templates,
                            ReceptionService receptionService, UserTaggingProcessor userTaggingProcessor) {
        this.sequences = sequences;
        this.db = db;
        this.supplierService = supplierService;
        this.emailSender = emailSender;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.templates = templates;
        this.receptionService = receptionService;
        this.userTaggingProcessor = userTaggingProcessor;
    }


    public void addToWishlist(UUID accountId, List<PurchaseWishlistItem> items) {
        db.batch(
                items.stream()
                        .map(item -> db.insertInto(PURCHASE_WISHLIST)
                                .set(PURCHASE_WISHLIST.ID, UUID.randomUUID())
                                .set(PURCHASE_WISHLIST.OWNER_ID, accountId)
                                .set(PURCHASE_WISHLIST.MATERIAL_GOOD_ID, item.materialGoodId())
                                .set(PURCHASE_WISHLIST.QUANTITY, item.quantity())
                                .set(PURCHASE_WISHLIST.EXPECTED_PRICE_AMOUNT, item.expectedPrice() != null ? item.expectedPrice().amount() : null)
                                .set(PURCHASE_WISHLIST.EXPECTED_PRICE_CURRENCY, item.expectedPrice() != null ? item.expectedPrice().currency() : null)
                                .set(PURCHASE_WISHLIST.SUPPLIER_ID, item.supplierId())
                                .onConflict(PURCHASE_WISHLIST.OWNER_ID, PURCHASE_WISHLIST.MATERIAL_GOOD_ID, PURCHASE_WISHLIST.SUPPLIER_ID)
                                .doUpdate().set(PURCHASE_WISHLIST.QUANTITY, PURCHASE_WISHLIST.QUANTITY.add(item.quantity()))
                        )
                        .collect(toList())
        ).execute();
    }

    public void updateWishlistItem(UUID wishlistItemId, UUID accountId, PurchaseWishlistItem item) {
        db.update(PURCHASE_WISHLIST)
                .set(PURCHASE_WISHLIST.QUANTITY, item.quantity())
                .set(PURCHASE_WISHLIST.EXPECTED_PRICE_AMOUNT, item.expectedPrice() != null ? item.expectedPrice().amount() : null)
                .set(PURCHASE_WISHLIST.EXPECTED_PRICE_CURRENCY, item.expectedPrice() != null ? item.expectedPrice().currency() : null)
                .where(PURCHASE_WISHLIST.ID.eq(wishlistItemId), PURCHASE_WISHLIST.OWNER_ID.eq(accountId))
                .execute();
    }

    public PurchaseOrder createPurchaseOrder(UUID accountId, UUID supplierId, List<PurchaseOrder.Item> items, EmailMessage emailMessage, PurchaseOrder.RenderingDetails renderingDetails, UUID managedBy) {
        var order = new PurchaseOrder(UUID.randomUUID(), null, null, false,
                accountId, supplierId, PurchaseOrder.Status.SUBMITTED,
                sequences.nextSequenceForPurchaseOrder(accountId),
                expectedDeliveryDateConsideringWeekend(LocalDate.now(), supplierService.lastOrderDeliveredIn(accountId, supplierId).orElse(1)),
                null, items, renderingDetails, managedBy);
        db.insertInto(PURCHASE_ORDER)
                .set(PURCHASE_ORDER.ID, order.id())
                .set(PURCHASE_ORDER.OWNER_ID, accountId)
                .set(PURCHASE_ORDER.SUPPLIER_ID, supplierId)
                .set(PURCHASE_ORDER.STATUS, PurchaseOrder.Status.SUBMITTED.name())
                .set(PURCHASE_ORDER.NUMBER, order.number())
                .set(PURCHASE_ORDER.EXPECTED_BY, order.expectedBy().atStartOfDay())
                .set(PURCHASE_ORDER.ITEMS, JSONB.valueOf(Json.write(order.items())))
                .set(PURCHASE_ORDER.RENDERING_DETAILS, JSONB.valueOf(Json.write(order.renderingDetails())))
                .set(PURCHASE_ORDER.MANAGED_BY, managedBy)
                .execute();
        //todo: validate that all the wishlist ids exist
        db.batch(
                items.stream()
                        .filter(item -> item.originalWishlistId() != null)
                        .map(item -> db.deleteFrom(PURCHASE_WISHLIST)
                                .where(PURCHASE_WISHLIST.ID.eq(item.originalWishlistId())))
                        .collect(toList())
        ).execute();
        if (emailMessage != null) {
            send(accountId, order.id(), emailMessage.subject(), emailMessage.body());
        }
        return order;
    }

    public PurchaseOrder update(UUID ownerId, UUID orderId, LocalDate expectedBy, PurchaseOrder.RenderingDetails renderingDetails, UUID managedBy) {
        var update = db.update(PURCHASE_ORDER)
                .set(PURCHASE_ORDER.EXPECTED_BY, expectedBy.atStartOfDay());
        if (renderingDetails != null) {
            update = update.set(PURCHASE_ORDER.RENDERING_DETAILS, JSONB.valueOf(Json.write(renderingDetails)));
        }
        if (managedBy != null) {
            update = update.set(PURCHASE_ORDER.MANAGED_BY, managedBy);
        }
        update.where(PURCHASE_ORDER.OWNER_ID.eq(ownerId), PURCHASE_ORDER.ID.eq(orderId))
                .execute();
        return order(ownerId, orderId);
    }

    public void cancel(UUID accountId, UUID orderId) {
        var order = order(accountId, orderId);
        if (order.status() == PurchaseOrder.Status.DELIVERED) {
            throw new BusinessException("you can't cancel DELIVERED orders", "purchase_order_is_delivered");
        }
        var affected = db.update(PURCHASE_ORDER)
                .set(PURCHASE_ORDER.STATUS, PurchaseOrder.Status.CANCELED.name())
                .where(PURCHASE_ORDER.OWNER_ID.eq(accountId), PURCHASE_ORDER.ID.eq(orderId))
                .execute();
        if (affected != 1) {
            throw new ResourceNotFoundException("Purchase order not found", "not_found");
        }
    }

    public void markOrderDelivered(UUID accountId, UUID orderId,
                                   UUID supplierId,
                                   LocalDate localDate,
                                   SupportingDocument supportingDocument,
                                   Currency currency,
                                   String notes,
                                   String receivedBy,
                                   String transportedBy,
                                   String transportedWith,
                                   List<ReceivedGood> goods,
                                   List<AdditionalCost> additionalCosts,
                                   List<Tuple.Tuple2<String, ByteArrayInputStream>> files
    ) {
        var affected = db.update(PURCHASE_ORDER)
                .set(PURCHASE_ORDER.STATUS, PurchaseOrder.Status.DELIVERED.name())
                .set(PURCHASE_ORDER.DELIVERED_AT, LocalDateTime.now())
                .where(PURCHASE_ORDER.OWNER_ID.eq(accountId), PURCHASE_ORDER.ID.eq(orderId), PURCHASE_ORDER.STATUS.eq(PurchaseOrder.Status.SUBMITTED.name()))
                .execute();
        if (affected != 1) {
            throw new ResourceNotFoundException("Purchase order not found", "not_found");
        }
        receptionService.receive(accountId, supplierId, orderId, null, localDate, supportingDocument, currency, notes,
                receivedBy, transportedBy, transportedWith, goods, additionalCosts, files);
    }

    public String html(PurchaseOrder order) {
        var account = account(order.ownerId());
        var renderingDetails = order.renderingDetails();
        var localizer = new Localizer((renderingDetails != null && renderingDetails.language() != null) ? new Locale(renderingDetails.language()) : new Locale(account.information().address().country()));
        var itemsWithPrice = order.items().stream()
                .filter(item -> item.price() != null)
                .toList();
        var subTotalAmount = itemsWithPrice.stream()
                .map(item -> item.quantity().multiply(BigDecimal.valueOf(item.price().amount())).longValue())
                .mapToLong(Long::longValue).sum();
        var currency = order.items().stream()
                .filter(item -> item.price() != null)
                .map(item -> item.price().currency())
                .findFirst().orElse(account.settings().general().defaultCurrency());
        var itemIndexer = new AtomicInteger();

        var orderMap = new HashMap<String, Object>();
        orderMap.put("number", order.number());
        orderMap.put("creationDate", localizer.localizeDate(Instant.now()));
        orderMap.put("expectedDate", localizer.localizeDate(order.expectedBy() != null
                ? order.expectedBy()
                : expectedDeliveryDateConsideringWeekend(
                LocalDate.now(),
                supplierService.lastOrderDeliveredIn(account.id(), order.supplierId()).orElse(1)
        )));
        orderMap.put("items", order.items().stream()
                .map(item -> {
                    var itemMap = new HashMap<String, Object>();
                    itemMap.put("index", itemIndexer.incrementAndGet());
                    itemMap.put("materialName", materialName(item.materialGoodId()));
                    itemMap.put("quantity", item.quantity());
                    itemMap.put("price", item.price());
                    return itemMap;
                })
                .toList());
        orderMap.put("totalUnits", order.items().stream()
                .map(PurchaseOrder.Item::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        orderMap.put("subTotalAmount", new Money(subTotalAmount, currency));
        orderMap.put("totalAmount", new Money(subTotalAmount, currency));
        orderMap.put("hasPrice", !itemsWithPrice.isEmpty());

        var map = new HashMap<String, Object>();
        map.put("title", renderingDetails != null ? renderingDetails.title() : null);
        map.put("order", orderMap);
        map.put("supplier", supplier(order.supplierId()));
        var accountMap = new HashMap<String, Object>();
        accountMap.put("name", account.name());
        accountMap.put("address", account.information().address());
        accountMap.put("phone", account.information().phone());
        accountMap.put("email", account.information().email());
        map.put("account", accountMap);
        map.put("labels", localizer.labels());
        map.put("base64Logo", account.logo());
        map.put("currency", currency);
        map.put("showPrice", renderingDetails == null || renderingDetails.columnsToHide() == null || !renderingDetails.columnsToHide().contains(PurchaseOrder.RenderingDetails.HideableColumn.PRICE));
        map.put("showQuantity", renderingDetails == null || renderingDetails.columnsToHide() == null || !renderingDetails.columnsToHide().contains(PurchaseOrder.RenderingDetails.HideableColumn.QUANTITY));

        return templates.render("pdf/purchase_order.html", map);
    }

    public byte[] pdf(String html) {
        return htmlToPdfConverter.convert(html);
    }

    public void sendForQuote(UUID ownerId, UUID orderId) {
        var order = db.selectFrom(PURCHASE_ORDER)
                .where(
                        PURCHASE_ORDER.OWNER_ID.eq(ownerId),
                        PURCHASE_ORDER.DELETED.isFalse(),
                        PURCHASE_ORDER.ID.eq(orderId)
                )
                .fetchSingleInto(PurchaseOrder.class);
        if (PurchaseOrder.Status.SUBMITTED == order.status()) {
            throw new BusinessException("Order is not in the correct state", "invalid_order_state");
        }
        int updated = db.update(PURCHASE_ORDER)
                .set(PURCHASE_ORDER.STATUS, PurchaseOrder.Status.SENT_FOR_QUOTE.name())
                .where(PURCHASE_ORDER.OWNER_ID.eq(ownerId), PURCHASE_ORDER.ID.eq(orderId))
                .execute();
        if (updated != 1) {
            throw new BusinessException("Order is not in the correct state", "invalid_order_state");
        }
    }

    public void send(UUID ownerId, UUID orderId, String subject, String body) {
        var order = db.selectFrom(PURCHASE_ORDER)
                .where(
                        PURCHASE_ORDER.OWNER_ID.eq(ownerId),
                        PURCHASE_ORDER.DELETED.isFalse(),
                        PURCHASE_ORDER.ID.eq(orderId)
                )
                .fetchSingleInto(PurchaseOrder.class);
        if (!List.of(PurchaseOrder.Status.SUBMITTED, PurchaseOrder.Status.SENT_FOR_QUOTE).contains(order.status())) {
            throw new BusinessException("Order is not in the correct state", "invalid_order_state");
        }
        db.update(PURCHASE_ORDER)
                .set(PURCHASE_ORDER.STATUS, PurchaseOrder.Status.SENT.name())
                .where(PURCHASE_ORDER.OWNER_ID.eq(ownerId), PURCHASE_ORDER.ID.eq(orderId))
                .execute();
        if (isNotBlank(subject) && isNotBlank(body)) {
            var supplier = supplier(order.supplierId());
            final byte[] pdf = pdf(html(order));
            emailSender.send(new Email(
                    "<EMAIL>",
                    List.of(supplierEmail(supplier)),
                    List.of(),
                    subject,
                    templates.render("email/base_template.html", Map.of(
                            "title", "Purchase Order",
                            "content", body
                    )),
                    Map.of("purchase_order_" + order.number() + ".pdf", pdf)
            ));
        }
    }

    public LocalDate expectedDeliveryDateConsideringWeekend(LocalDate start, int daysForDelivery) {
        var date = start;
        while (daysForDelivery > 0) {
            if (date.getDayOfWeek() == DayOfWeek.SUNDAY) {
                date = date.plusDays(1);
                continue;
            }
            date = date.plusDays(1);
            daysForDelivery--;
        }
        return date;
    }

    public Note addNote(UUID ownerId, UUID orderId, UUID userId, String note) {
        var id = UUID.randomUUID();
        db.insertInto(NOTE)
                .set(NOTE.ID, id)
                .set(NOTE.OWNER_ID, ownerId)
                .set(NOTE.ADDED_BY_ID, userId)
                .set(NOTE.NOTE_, note)
                .execute();
        db.insertInto(PURCHASE_ORDER_NOTE)
                .set(PURCHASE_ORDER_NOTE.NOTE_ID, id)
                .set(PURCHASE_ORDER_NOTE.PURCHASE_ORDER_ID, orderId)
                .execute();
        userTaggingProcessor.notify(ownerId, Notification.Section.PURCHASES, orderId, userId, id, note, number(orderId));
        return new Note(id, Instant.now(), Instant.now(), false, ownerId, userId, note);
    }

    private Company supplier(UUID supplierId) {
        return db.selectFrom(COMPANY)
                .where(COMPANY.ID.eq(supplierId))
                .fetchSingle()
                .into(Company.class);
    }

    private String supplierEmail(Company supplier) {
        return supplier
                .details().contacts().stream()
                .map(ContactPerson::email)
                .findAny()
                .orElseThrow(() -> new RuntimeException("Cannot find suitable supplier email address."));
    }


    private Account account(UUID accountId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountId))
                .fetchSingle()
                .into(Account.class);
    }

    private String materialName(UUID materialGoodId) {
        return db.select(MATERIAL_GOOD.NAME)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialGoodId))
                .fetchSingleInto(String.class);
    }

    private PurchaseOrder order(UUID accountId, UUID orderId) {
        return db.selectFrom(PURCHASE_ORDER)
                .where(PURCHASE_ORDER.OWNER_ID.eq(accountId), PURCHASE_ORDER.ID.eq(orderId))
                .fetchSingleInto(PurchaseOrder.class);
    }

    private String number(UUID orderId) {
        return db.select(PURCHASE_ORDER.NUMBER)
                .from(PURCHASE_ORDER)
                .where(PURCHASE_ORDER.ID.eq(orderId))
                .fetchSingleInto(String.class);
    }

}
